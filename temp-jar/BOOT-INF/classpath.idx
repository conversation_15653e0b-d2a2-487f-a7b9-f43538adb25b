- "BOOT-INF/lib/spring-boot-2.7.14.jar"
- "BOOT-INF/lib/spring-boot-autoconfigure-2.7.14.jar"
- "BOOT-INF/lib/logback-classic-1.2.12.jar"
- "BOOT-INF/lib/logback-core-1.2.12.jar"
- "BOOT-INF/lib/log4j-to-slf4j-2.17.2.jar"
- "BOOT-INF/lib/log4j-api-2.17.2.jar"
- "BOOT-INF/lib/jul-to-slf4j-1.7.36.jar"
- "BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar"
- "BOOT-INF/lib/snakeyaml-1.30.jar"
- "BOOT-INF/lib/jackson-databind-2.13.5.jar"
- "BOOT-INF/lib/jackson-core-2.13.5.jar"
- "BOOT-INF/lib/jackson-datatype-jdk8-2.13.5.jar"
- "BOOT-INF/lib/jackson-datatype-jsr310-2.13.5.jar"
- "BOOT-INF/lib/jackson-module-parameter-names-2.13.5.jar"
- "BOOT-INF/lib/tomcat-embed-core-9.0.78.jar"
- "BOOT-INF/lib/tomcat-embed-websocket-9.0.78.jar"
- "BOOT-INF/lib/spring-web-5.3.29.jar"
- "BOOT-INF/lib/spring-beans-5.3.29.jar"
- "BOOT-INF/lib/spring-webmvc-5.3.29.jar"
- "BOOT-INF/lib/spring-aop-5.3.29.jar"
- "BOOT-INF/lib/spring-context-5.3.29.jar"
- "BOOT-INF/lib/spring-expression-5.3.29.jar"
- "BOOT-INF/lib/tomcat-embed-el-9.0.78.jar"
- "BOOT-INF/lib/hibernate-validator-6.2.5.Final.jar"
- "BOOT-INF/lib/jakarta.validation-api-2.0.2.jar"
- "BOOT-INF/lib/jboss-logging-3.4.3.Final.jar"
- "BOOT-INF/lib/classmate-1.5.1.jar"
- "BOOT-INF/lib/spring-context-support-5.3.29.jar"
- "BOOT-INF/lib/sdk-java-5.0.6.jar"
- "BOOT-INF/lib/okhttp-4.9.3.jar"
- "BOOT-INF/lib/okhttp-sse-4.9.3.jar"
- "BOOT-INF/lib/kotlin-stdlib-jdk8-1.6.21.jar"
- "BOOT-INF/lib/kotlin-stdlib-jdk7-1.6.21.jar"
- "BOOT-INF/lib/logging-interceptor-4.9.3.jar"
- "BOOT-INF/lib/okio-2.8.0.jar"
- "BOOT-INF/lib/kotlin-stdlib-common-1.6.21.jar"
- "BOOT-INF/lib/kotlin-stdlib-1.4.10.jar"
- "BOOT-INF/lib/annotations-13.0.jar"
- "BOOT-INF/lib/fastjson-1.2.83.jar"
- "BOOT-INF/lib/knife4j-openapi2-spring-boot-starter-4.1.0.jar"
- "BOOT-INF/lib/knife4j-core-4.1.0.jar"
- "BOOT-INF/lib/slf4j-api-1.7.36.jar"
- "BOOT-INF/lib/lombok-1.18.28.jar"
- "BOOT-INF/lib/knife4j-openapi2-ui-4.1.0.jar"
- "BOOT-INF/lib/javassist-3.25.0-GA.jar"
- "BOOT-INF/lib/springfox-swagger2-2.10.5.jar"
- "BOOT-INF/lib/springfox-spi-2.10.5.jar"
- "BOOT-INF/lib/springfox-core-2.10.5.jar"
- "BOOT-INF/lib/springfox-schema-2.10.5.jar"
- "BOOT-INF/lib/springfox-swagger-common-2.10.5.jar"
- "BOOT-INF/lib/springfox-spring-web-2.10.5.jar"
- "BOOT-INF/lib/classgraph-4.1.7.jar"
- "BOOT-INF/lib/spring-plugin-core-2.0.0.RELEASE.jar"
- "BOOT-INF/lib/spring-plugin-metadata-2.0.0.RELEASE.jar"
- "BOOT-INF/lib/mapstruct-1.3.1.Final.jar"
- "BOOT-INF/lib/swagger-models-1.6.6.jar"
- "BOOT-INF/lib/jackson-annotations-2.13.5.jar"
- "BOOT-INF/lib/swagger-annotations-1.6.6.jar"
- "BOOT-INF/lib/springfox-bean-validators-2.10.5.jar"
- "BOOT-INF/lib/springfox-spring-webmvc-2.10.5.jar"
- "BOOT-INF/lib/byte-buddy-1.12.23.jar"
- "BOOT-INF/lib/spring-core-5.3.29.jar"
- "BOOT-INF/lib/spring-jcl-5.3.29.jar"
- "BOOT-INF/lib/spring-boot-jarmode-layertools-2.7.14.jar"
