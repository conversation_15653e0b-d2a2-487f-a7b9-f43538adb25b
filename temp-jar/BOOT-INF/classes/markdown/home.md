# 紫鸟AI穿衣API文档

欢迎使用紫鸟AI穿衣API接口文档！

## 功能介绍

本API提供AI穿衣功能，支持：
- 上传人物图片和服装图片
- 生成穿衣效果图
- 支持多种服装类型（上衣、下装、连衣裙等）
- 异步任务处理和状态查询

## 快速开始

### 1. 配置API密钥

在 `application.yml` 中配置您的应用信息：

```yaml
ziniao:
  api:
    app-id: your_app_id_here
    private-key: your_private_key_here
```

### 2. 获取应用令牌

首先调用 `/api/token/app` 接口获取应用令牌。

### 3. 调用AI穿衣接口

使用 `/api/clothing/process` 接口进行AI穿衣处理。

## 接口说明

### 令牌管理
- `GET /api/token/app` - 获取应用令牌
- `POST /api/token/app/refresh` - 刷新应用令牌

### AI穿衣
- `POST /api/clothing/process` - AI穿衣处理
- `GET /api/clothing/task/{taskId}` - 查询任务状态
- `POST /api/clothing/task/{taskId}/wait` - 等待任务完成

## 注意事项

1. **API密钥安全**：请妥善保管您的应用私钥
2. **图片格式**：支持URL链接或base64编码
3. **请求频率**：请注意API的调用频率限制
4. **异步处理**：部分任务可能需要异步处理，请使用任务状态查询接口

## 联系支持

如有问题，请参考紫鸟开放平台官方文档或联系技术支持。
