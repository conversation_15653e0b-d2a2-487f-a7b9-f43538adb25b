server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: ziniao-ai-demo

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 200MB
      max-request-size: 200MB
      file-size-threshold: 2KB

  # 缓存配置
  cache:
    type: simple
    cache-names:
      - appToken

  # 解决Spring Boot 2.6+与Swagger兼容性问题
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

# Swagger配置（现在启用，显示中文接口名称）
swagger:
  enabled: true

# 紫鸟API配置
ziniao:
  api:
    base-url: https://sbappstoreapi.ziniao.com
    app-id: 202507121393517203126611968
    private-key: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDERMWtqBpMyLnjLsJ0DS4PtYigGEN1Q+E3oaVe+U5jG9D3BjPf5a6YUAr1mBnLD2LFUtwEV0qQeYQNnWshU7lahKsTkPuVupOgWMMRm5dGghp75bn7zRf+x+zgvSqwpFM1pMd/X/TlX6Ab0aBrgoRu4GdMssf5XlWT8abiraAzFCi7mhO6xEUuRW9Kzy1dYLHnvWuCYNgvkbyYbny/S9DUkFlsHzq2pn99Wxx8ZCqkOHwyKXjv7XfDtR9mqazXdVmie07FbAaOEYqoyZ5Mq/DtELXuhtEtdNXSvwSK9QeC6psL067Yc/CaIfIbVoYk9R5ZzHZpMaLowz8F65nNYuCLAgMBAAECggEAUtm5HKpWl80v8v/dJAIgTI9UIki7yTejTcmVtnbFKT8nwe9DXKNnRXKcfbn8QWS4TBXDPK3gkwZIwTqPa50dJGHWmcbc/QKqqMhvZ/JnNGNdbKm4Ddww4eNhUiloaPKlEYMCl/lYtq7cNoISFvAcmXVvBch+c/znYTFkQlIKyyRcrOhtNkRvCp6eeWSZc7C7oQUpLQDQAju4r/e2m/6BGgiYcGL4DIs5nB0M3M8NKPWfiDMLX5kNMZk3LaygdNlvDNS/vqYHayQW3t1ZpIWLykkmdyrchTo2CP0kpjyyukNcq1dTipCetvxg/uQQjLdG7fGD9aaPsrQwC/IeRRIe2QKBgQDwVGZxnbsNGwzNg5APifPvuKFEiStwbZtIZXLO4pbAQxHR65/rrqg0QenNcMbn2hN76IpWTml20U6XHlCHialHHfCYUA7fRjb7Pz8M3Mo5gpN6t7SqvMSah9hKCSfNp3l4elHd/6HOTqklMU9tznSvmtrvExSlEa+2/GwulWtHnwKBgQDREObmy9bW+hUY7VohDX1BM2fTfHrYDv3ueC13ntPedI1IzMeq57k+CJHTdxrEcPhK5pgF8rr0DPa221or8Yyhe6Hssv6nxjAjGYOtw24xKFFFGBF5qB6pSbS3YpOwCO6AxcC2ofIX3EVXbajAN9sAJSndg2n1o5cqbT/rOLQvlQKBgD2Lb5yi5vbpOXMbwPWpv1Vhc7MIaZ5mA0Eb8bW3YyGjS1n1vENvu9V+F7Crs9RGDX1KxLprZCiwWy7p7Xd4Mmhq62UmA1j8MzJONQhHNmZZ4QipKNQceUCVjzcOdyn8PGg08ugmAbrCfCBxK0UUfN9Hic91lHDdQaFbrRyU2R9DAoGAKwihnsxCksmzYggpTzhpmews6Plsh+C+IEIYidDlp5qyIlQUnXdJSB2XdsHDfsAKO9CvZjoKhYXNmuIX84eq/Opn8EL/7CT9b5wm4LOjkPbNk71ai4IxnYMQcdcWs1uDTHpoKq+3F3Y9x7tIyxg0OhlsPq54NxnPWepXB+IKJOkCgYBlWkaXRdZb63dYkDyKRSaIKz61WtXPdxk16FCCsNyTdkNobiEsfD+QJ7njY98/Hm00LYE5R9fNXq0U+CJEYKlilhc2ab4CH4S6TdKlVld//nPdr3K4N+H8yhvUlxheS1LCIPQYWM8BEhjWSiVvqNnFnbCk1DhbolVj7R1QqhzMvQ==
    timeout: 30000
    token-cache-time: 7200

# 文件上传配置
file:
  upload:
    # 上传文件存储路径（使用项目根目录下的uploads文件夹）
    path: ${user.dir}/uploads/
    # 最大文件大小（字节）200MB
    max-size: 209715200
    # 允许的文件类型
    allowed-types: jpg,jpeg,png,gif,bmp,webp,pdf,doc,docx,txt,zip,rar,mp4,avi,mov,mp3,wav
    # 服务器访问URL前缀
    url-prefix: /uploads/

# 日志配置
logging:
  level:
    com.ziniao: DEBUG
    com.gitee.sop: INFO
    org.springframework: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ziniao-ai-demo.log
    max-size: 10MB
    max-history: 30

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh-CN
    enable-version: true
    enable-reload-cache-parameter: true
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-host: false
    enable-host-text: ""
    enable-home-custom: true
    home-custom-path: classpath:markdown/home.md
    enable-search: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: "Copyright © 2024 紫鸟AI穿衣Demo"
    enable-dynamic-parameter: true
    enable-debug: true
    enable-open-api: false
    enable-group: true
