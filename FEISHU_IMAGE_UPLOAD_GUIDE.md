# 🖼️ 飞书图片上传到本地服务器解决方案

## 问题背景

你遇到的错误：
```json
{
  "code": 99991661,
  "msg": "Missing access token for authorization. Please make a request with token attached.",
  "error": {
    "log_id": "2025072500231299C5342E32D62213B3AB",
    "troubleshooter": "排查建议查看 (Troubleshooting suggestions): https://open.feishu.cn/search?from=openapi&log_id=2025072500231299C5342E32D62213B3AB&code=99991661&method_id=6944991842538831873"
  }
}
```

**根本原因**：飞书API请求缺少必要的 `Authorization` 头，或者token已过期。

## 解决方案

### 1. 增强的Token处理机制

我已经为你优化了 `FeishuBitableService.java` 中的图片下载逻辑：

#### ✅ 自动Token管理
- 自动获取和缓存飞书访问令牌
- 检测token过期并自动刷新
- 在每个图片下载请求中正确设置 `Authorization` 头

#### ✅ 错误检测和重试
- 检测 `99991661`（缺少token）错误
- 检测 `99991663`（token过期）错误  
- 检测 `99991664`（token无效）错误
- 自动重试机制

#### ✅ 完整的请求头设置
```java
connection.setRequestProperty("Authorization", "Bearer " + accessToken);
connection.setRequestProperty("User-Agent", "Mozilla/5.0 (compatible; FeishuImageDownloader/1.0)");
connection.setRequestProperty("Accept", "image/*,*/*;q=0.8");
```

### 2. 新增的API接口

#### 🆕 增强版图片上传接口
```
POST /api/feishu/bitable/images/upload-to-local
```

**特点**：
- 专门用于获取飞书图片并上传到本地服务器
- 内置token认证错误处理
- 合理的超时和并发控制
- 详细的错误日志和状态反馈

## 使用方法

### 1. 基本请求示例

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "你的多维表格appToken",
    "tableId": "你的表格ID", 
    "viewId": "你的视图ID",
    "downloadToLocal": true,
    "pageSize": 10,
    "includeImageDetails": true,
    "downloadTimeout": 60,
    "maxConcurrentDownloads": 3
  }'
```

### 2. 请求参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `appToken` | String | ✅ | 多维表格的唯一标识符 |
| `tableId` | String | ✅ | 数据表的唯一标识符 |
| `viewId` | String | ❌ | 视图ID，不传则使用默认视图 |
| `recordId` | String | ❌ | 指定记录ID，只处理该记录的图片 |
| `imageFields` | Array | ❌ | 指定图片字段名，不传则处理所有图片字段 |
| `downloadToLocal` | Boolean | ❌ | 是否下载到本地，默认true |
| `pageSize` | Integer | ❌ | 分页大小，默认10，最大500 |
| `downloadTimeout` | Integer | ❌ | 下载超时时间（秒），默认60 |
| `maxConcurrentDownloads` | Integer | ❌ | 最大并发下载数，默认3 |

### 3. 成功响应示例

```json
{
  "code": 200,
  "message": "处理成功",
  "success": true,
  "data": {
    "totalImages": 15,
    "successfulDownloads": 13,
    "failedDownloads": 2,
    "processedRecords": 5,
    "imageDetails": [
      {
        "recordId": "recABC123",
        "fieldName": "图片",
        "originalName": "example.jpg",
        "downloadStatus": "SUCCESS",
        "localAccessUrl": "http://localhost:8080/uploads/2025/07/24/20250724_123456_abc123.jpg",
        "downloadTime": "2025-07-24 12:34:56"
      }
    ]
  }
}
```

### 4. 错误响应示例

```json
{
  "code": 400,
  "message": "获取表格记录失败",
  "success": false,
  "data": null
}
```

## 测试验证

### 1. 运行测试脚本

```bash
# 给脚本执行权限
chmod +x test-feishu-image-upload.sh

# 运行测试
./test-feishu-image-upload.sh
```

### 2. 手动测试步骤

1. **检查服务器状态**
   ```bash
   curl -X GET "http://localhost:8080/api/clothing/health"
   ```

2. **获取服务信息**
   ```bash
   curl -X GET "http://localhost:8080/api/feishu/bitable/info"
   ```

3. **测试图片上传**
   ```bash
   curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
     -H "Content-Type: application/json" \
     -d '{"appToken":"你的appToken","tableId":"你的tableId"}'
   ```

## 配置检查

### 1. 飞书应用配置

确保 `application.yml` 中的飞书配置正确：

```yaml
feishu:
  api:
    base-url: https://open.feishu.cn
    app-id: 你的应用ID
    app-secret: 你的应用密钥
    timeout: 30000
    token-cache-time: 7000
    connect-timeout: 10000
    read-timeout: 30000
```

### 2. 应用权限检查

确保飞书应用具有以下权限：
- `bitable:app` - 获取多维表格信息
- `bitable:app:readonly` - 读取多维表格数据
- `drive:drive:readonly` - 读取云文档文件

### 3. IP白名单

如果遇到IP访问限制，需要在飞书开放平台添加服务器IP到白名单。

## 故障排除

### 常见错误及解决方案

| 错误码 | 错误信息 | 解决方案 |
|--------|----------|----------|
| 99991661 | Missing access token | ✅ 已修复：自动添加Authorization头 |
| 99991663 | Token expired | ✅ 已修复：自动刷新token并重试 |
| 99991664 | Token invalid | ✅ 已修复：强制获取新token |
| 99991401 | IP denied | 需要在飞书平台添加IP白名单 |
| 91402 | App not exist | 检查appToken是否正确 |

### 日志查看

查看详细的处理日志：
```bash
tail -f logs/ziniao-api.log | grep -E "(FeishuBitableService|图片下载)"
```

## 总结

通过这个解决方案，你现在可以：

1. ✅ **避免token认证错误**：自动管理token生命周期
2. ✅ **稳定的图片下载**：内置重试机制和错误处理
3. ✅ **本地服务器存储**：图片自动上传到你的服务器
4. ✅ **详细的状态反馈**：清楚了解处理结果
5. ✅ **灵活的参数控制**：支持分页、字段筛选等

现在你可以放心使用 `/api/feishu/bitable/images/upload-to-local` 接口来获取飞书图片并上传到本地服务器了！
