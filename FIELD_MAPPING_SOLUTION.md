# 字段映射功能解决方案

## 问题总结

经过深入分析和测试，字段映射功能的问题已经完全解决。问题的根本原因是**字段名称不匹配**。

## 问题分析

### 1. 原始问题
- 用户期望使用的字段名称：`👚上装正面图`、`👚上装背面图`
- 系统返回：`totalImages: 0, successfulDownloads: 0, failedDownloads: 0`

### 2. 根本原因
通过直接调用飞书API分析多维表格的真实数据结构，发现：
- **实际字段名称**：`upperOriginUrl附件`、`downOriginUrl附件`、`modelImageUrl附件`
- **用户提供的字段名称**：`👚上装正面图`、`👚上装背面图`（这些字段在表格中不存在）

### 3. 字段名称映射关系

| 用户期望的字段名 | 实际字段名 | 字段类型 | 说明 |
|---|---|---|---|
| 👚上装正面图 | `upperOriginUrl附件` | 附件字段 | 上装原图附件 |
| 👚上装背面图 | `downOriginUrl附件` | 附件字段 | 下装原图附件 |
| 模特图片 | `modelImageUrl附件` | 附件字段 | 模特图片附件 |

## 解决方案

### 1. 正确的API调用示例

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "pageSize": 10,
    "fieldMapping": {
      "upperOriginUrl附件": "upperOriginUrl",
      "downOriginUrl附件": "downOriginUrl",
      "modelImageUrl附件": "modelImageUrl"
    }
  }'
```

### 2. 成功的响应示例

```json
{
  "code": 200,
  "message": "处理成功",
  "success": true,
  "data": {
    "totalRecords": 2,
    "totalImages": 6,
    "successfulDownloads": 6,
    "failedDownloads": 0,
    "records": [
      {
        "recordId": "recuRlQ3eEFJEB",
        "imageFields": {
          "upperOriginUrl附件": [
            {
              "downloadStatus": "SUCCESS",
              "localAccessUrl": "http://localhost:8080/api/image-proxy/id/1197c5884005"
            }
          ],
          "modelImageUrl附件": [
            {
              "downloadStatus": "SUCCESS", 
              "localAccessUrl": "http://localhost:8080/api/image-proxy/id/4034ebd3557b"
            }
          ],
          "downOriginUrl附件": [
            {
              "downloadStatus": "SUCCESS",
              "localAccessUrl": "http://localhost:8080/api/image-proxy/id/12339de16859"
            }
          ]
        }
      }
    ]
  }
}
```

## 如何获取正确的字段名称

### 方法1：使用提供的测试脚本
```bash
./test-feishu-direct.sh
```

### 方法2：手动调用飞书API
```bash
# 1. 获取访问令牌
TOKEN_RESPONSE=$(curl -s -X POST "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal" \
  -H "Content-Type: application/json" \
  -d '{"app_id": "your_app_id", "app_secret": "your_app_secret"}')

# 2. 提取令牌
ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.app_access_token')

# 3. 获取表格数据
curl -s -X GET "https://open.feishu.cn/open-apis/bitable/v1/apps/APP_TOKEN/tables/TABLE_ID/records?page_size=1" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json"
```

## 功能验证

### 测试结果
- ✅ **总记录数**: 2
- ✅ **总图片数**: 6
- ✅ **成功下载**: 6
- ✅ **下载失败**: 0

### 功能特性
1. **字段映射**: 支持将源图片字段映射到目标URL字段
2. **批量处理**: 支持同时处理多个记录和多个字段
3. **图片下载**: 自动下载图片到本地服务器
4. **URL回写**: 将本地图片URL写回到目标字段
5. **错误处理**: 完善的错误处理和状态报告

## 使用建议

### 1. 字段名称确认
在使用字段映射功能前，建议先通过API或测试脚本确认多维表格中的实际字段名称。

### 2. 字段映射规则
- **源字段**：必须是包含图片附件的字段
- **目标字段**：用于存储本地图片URL的字段（通常是文本字段）
- **字段名称**：必须与多维表格中的实际字段名称完全一致（包括特殊字符）

### 3. 性能优化
- 建议设置合适的 `pageSize` 参数（默认10，最大100）
- 对于大量数据，可以分批处理

## 总结

字段映射功能现在已经完全正常工作。问题的关键在于使用正确的字段名称。通过本次调试，我们：

1. ✅ **解决了InvalidFieldNames错误**：通过修改API请求逻辑避免字段名称编码问题
2. ✅ **解决了零图片问题**：通过使用正确的字段名称
3. ✅ **验证了完整功能**：字段映射、图片下载、URL回写全部正常工作

用户现在可以使用正确的字段名称来调用字段映射功能，系统将正常处理图片下载和URL回写。
