#!/bin/bash

# 测试图片代理路径修复

echo "🔧 测试图片代理路径修复"
echo "======================"

SERVER_URL="http://39.108.93.224:18088"
LOG_FILE="/www/wwwroot/fs.vwo50.life_998/logs/ziniao-ai-demo.log"

echo ""
echo "1. 检查现有图片文件"
echo "-------------------"
echo "🔍 查找最近的图片文件:"
find /www/wwwroot/fs.vwo50.life_998/uploads -name "*.png" -mtime -1 | head -5

echo ""
echo "2. 测试代理URL生成"
echo "------------------"
echo "🧪 生成代理URL:"
PROXY_RESPONSE=$(curl -s -X POST \
  -d "filePath=/uploads/2025/07/26/20250726_002549_8c5aa3e6.png" \
  "$SERVER_URL/api/image-proxy/generate-url")

echo "$PROXY_RESPONSE"

# 提取代理URL
PROXY_URL=$(echo "$PROXY_RESPONSE" | jq -r '.proxyUrl' 2>/dev/null)
if [ "$PROXY_URL" != "null" ] && [ -n "$PROXY_URL" ]; then
    echo "✅ 代理URL生成成功: $PROXY_URL"
    
    # 提取图片ID
    IMAGE_ID=$(echo "$PROXY_URL" | sed 's/.*\/id\///')
    echo "📋 提取的图片ID: $IMAGE_ID"
    
    echo ""
    echo "3. 测试代理URL访问"
    echo "------------------"
    echo "🧪 访问代理URL:"
    curl -s -I "$PROXY_URL" | head -3
    
    echo ""
    echo "🧪 下载图片测试:"
    curl -s "$PROXY_URL" -o test-downloaded-image.png
    if [ -f "test-downloaded-image.png" ]; then
        FILE_SIZE=$(stat -f%z test-downloaded-image.png 2>/dev/null || stat -c%s test-downloaded-image.png 2>/dev/null)
        if [ "$FILE_SIZE" -gt 1000 ]; then
            echo "✅ 图片下载成功，大小: ${FILE_SIZE} bytes"
            file test-downloaded-image.png
        else
            echo "❌ 图片下载失败或文件太小: ${FILE_SIZE} bytes"
            head -c 200 test-downloaded-image.png
        fi
        rm -f test-downloaded-image.png
    else
        echo "❌ 图片下载失败"
    fi
else
    echo "❌ 代理URL生成失败"
fi

echo ""
echo "4. 测试特定图片字段接口"
echo "----------------------"
echo "🧪 调用特定图片字段处理接口:"
FIELD_RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "recordId": "recuRlQ3eEFJEB",
    "updateBitableWithLocalUrl": true
  }' \
  "$SERVER_URL/api/feishu/bitable/specific-image-fields")

echo "📋 响应状态:"
echo "$FIELD_RESPONSE" | jq '.success, .message' 2>/dev/null || echo "解析响应失败"

echo ""
echo "📋 检查返回的URL格式:"
LOCAL_URLS=$(echo "$FIELD_RESPONSE" | jq -r '.data.records[0].imageFields | to_entries | .[].value[0].localAccessUrl' 2>/dev/null)
if [ -n "$LOCAL_URLS" ]; then
    echo "$LOCAL_URLS" | while read -r url; do
        if [[ "$url" == *"/api/image-proxy/id/"* ]]; then
            echo "✅ 代理URL格式正确: $url"
        else
            echo "❌ 仍然是直接URL: $url"
        fi
    done
else
    echo "❌ 无法提取URL信息"
fi

echo ""
echo "5. 检查日志信息"
echo "---------------"
if [ -f "$LOG_FILE" ]; then
    echo "📋 最新的路径构建日志:"
    tail -n 100 "$LOG_FILE" | grep -E "(构建完整文件路径|buildFullPath|图片ID.*对应路径)" | tail -10
    
    echo ""
    echo "📋 最新的图片访问日志:"
    tail -n 50 "$LOG_FILE" | grep -E "(收到图片ID访问请求|图片文件不存在|成功找到图片文件)" | tail -5
else
    echo "❌ 日志文件不存在"
fi

echo ""
echo "6. 诊断信息"
echo "-----------"
echo "🔍 配置信息:"
echo "   - 服务器URL: $SERVER_URL"
echo "   - 上传路径: /www/wwwroot/fs.vwo50.life_998/uploads/"
echo "   - URL前缀: /uploads/"

echo ""
echo "🔍 路径转换示例:"
echo "   输入: /uploads/2025/07/26/test.png"
echo "   期望输出: /www/wwwroot/fs.vwo50.life_998/uploads/2025/07/26/test.png"

echo ""
echo "7. 手动验证"
echo "-----------"
echo "📝 手动测试命令:"
echo "# 生成代理URL"
echo "curl -X POST -d 'filePath=/uploads/2025/07/26/test.png' '$SERVER_URL/api/image-proxy/generate-url'"
echo ""
echo "# 访问代理URL（替换为实际的ID）"
echo "curl '$SERVER_URL/api/image-proxy/id/YOUR_IMAGE_ID'"

echo ""
echo "✅ 测试完成"
echo ""
echo "🎯 期望结果:"
echo "   1. 代理URL生成成功"
echo "   2. 代理URL可以正常访问"
echo "   3. 特定字段接口返回代理URL格式"
echo "   4. 日志显示正确的路径构建过程"
echo ""
echo "📞 如果问题仍然存在:"
echo "   1. 检查文件路径是否正确"
echo "   2. 确认上传目录权限"
echo "   3. 查看详细的调试日志"
