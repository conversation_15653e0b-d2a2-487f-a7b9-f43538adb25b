#!/bin/bash

# 飞书应用访问令牌测试脚本
# 用于验证飞书应用配置是否正确

echo "=== 飞书应用访问令牌测试 ==="
echo ""

# 你的飞书应用配置
APP_ID="cli_a8fe3e73bd78d00d"
APP_SECRET="whPto88DEdJ9n3uBiDoDNhlTEb3KAJLU"
BASE_URL="https://open.feishu.cn"

echo "📋 飞书应用配置:"
echo "  App ID: $APP_ID"
echo "  App Secret: ${APP_SECRET:0:10}..."
echo "  Base URL: $BASE_URL"
echo ""

# 1. 直接调用飞书API获取令牌
echo "🔑 1. 直接调用飞书API获取应用访问令牌"
echo "POST $BASE_URL/open-apis/auth/v3/app_access_token/internal"

response=$(curl -s -X POST "$BASE_URL/open-apis/auth/v3/app_access_token/internal" \
  -H "Content-Type: application/json" \
  -d "{
    \"app_id\": \"$APP_ID\",
    \"app_secret\": \"$APP_SECRET\"
  }" \
  -w "\n状态码: %{http_code}")

echo "$response"
echo ""

# 2. 解析响应
echo "🔍 2. 解析响应结果"
token_response=$(echo "$response" | head -n -1)
status_code=$(echo "$response" | tail -n 1 | grep -o '[0-9]*')

echo "状态码: $status_code"
echo "响应内容: $token_response"

# 检查是否成功
if echo "$token_response" | jq -e '.code == 0' > /dev/null 2>&1; then
    echo "✅ 令牌获取成功！"
    app_access_token=$(echo "$token_response" | jq -r '.app_access_token')
    echo "令牌: ${app_access_token:0:20}..."
    
    # 3. 测试令牌是否有效
    echo ""
    echo "🧪 3. 测试令牌有效性（获取应用信息）"
    curl -s -X GET "$BASE_URL/open-apis/application/v6/applications/$APP_ID" \
      -H "Authorization: Bearer $app_access_token" \
      -H "Content-Type: application/json" \
      -w "\n状态码: %{http_code}" | jq '.' 2>/dev/null || cat
    
else
    echo "❌ 令牌获取失败！"
    
    # 分析错误原因
    if echo "$token_response" | jq -e '.code' > /dev/null 2>&1; then
        error_code=$(echo "$token_response" | jq -r '.code')
        error_msg=$(echo "$token_response" | jq -r '.msg')
        echo "错误码: $error_code"
        echo "错误信息: $error_msg"
        
        case $error_code in
            10003)
                echo ""
                echo "🔧 错误分析: invalid param"
                echo "可能的原因："
                echo "1. app_id 格式不正确（应该以 cli_ 开头）"
                echo "2. app_secret 不正确或已过期"
                echo "3. 应用未启用或被禁用"
                echo "4. 请求参数格式错误"
                ;;
            10014)
                echo ""
                echo "🔧 错误分析: app not found"
                echo "可能的原因："
                echo "1. app_id 不存在"
                echo "2. 应用已被删除"
                ;;
            *)
                echo ""
                echo "🔧 其他错误，请查看飞书开放平台文档"
                ;;
        esac
    fi
fi

echo ""
echo "📝 故障排除建议:"
echo "1. 检查飞书开放平台应用配置"
echo "2. 确认 app_id 和 app_secret 正确"
echo "3. 确认应用状态为'已启用'"
echo "4. 检查应用权限配置"
echo "5. 如果是企业自建应用，确认在正确的企业下"

echo ""
echo "🔗 相关链接:"
echo "- 飞书开放平台: https://open.feishu.cn/"
echo "- 应用管理: https://open.feishu.cn/app"
echo "- API文档: https://open.feishu.cn/document/server-docs/authentication-management/access-token/app_access_token_internal"
