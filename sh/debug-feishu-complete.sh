#!/bin/bash

# 完整的飞书多维表格调试脚本
# 系统性地检查所有可能的问题

echo "=== 飞书多维表格完整调试 ==="
echo ""

# 配置信息
APP_ID="cli_a8fe3e73bd78d00d"
APP_SECRET="whPto88DEdJ9n3uBiDoDNhlTEb3KAJLU"
BASE_URL="https://open.feishu.cn"

# 你的多维表格参数
APP_TOKEN="Wc2WwTiksil7vVkE1hqcmCmmneb"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo "📋 配置信息:"
echo "  飞书App ID: $APP_ID"
echo "  多维表格Token: $APP_TOKEN"
echo "  数据表ID: $TABLE_ID"
echo "  视图ID: $VIEW_ID"
echo ""

# 步骤1: 获取应用访问令牌
echo "🔑 步骤1: 获取飞书应用访问令牌"
token_response=$(curl -s -w "HTTP_CODE:%{http_code}" -X POST "$BASE_URL/open-apis/auth/v3/app_access_token/internal" \
  -H "Content-Type: application/json" \
  -d "{
    \"app_id\": \"$APP_ID\",
    \"app_secret\": \"$APP_SECRET\"
  }")

# 分离HTTP状态码和响应体
http_code=$(echo "$token_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
response_body=$(echo "$token_response" | sed 's/HTTP_CODE:[0-9]*$//')

echo "HTTP状态码: $http_code"
echo "响应内容: $response_body"

if [ "$http_code" != "200" ]; then
    echo "❌ HTTP请求失败，状态码: $http_code"
    exit 1
fi

# 检查响应是否成功
if echo "$response_body" | grep -q '"code":0'; then
    echo "✅ 令牌获取成功"
    
    # 提取令牌
    app_access_token=$(echo "$response_body" | sed -n 's/.*"app_access_token":"\([^"]*\)".*/\1/p')
    echo "令牌: ${app_access_token:0:30}..."
    echo ""
    
else
    echo "❌ 令牌获取失败"
    error_code=$(echo "$response_body" | sed -n 's/.*"code":\([0-9]*\).*/\1/p')
    error_msg=$(echo "$response_body" | sed -n 's/.*"msg":"\([^"]*\)".*/\1/p')
    echo "错误码: $error_code"
    echo "错误信息: $error_msg"
    
    case $error_code in
        10003)
            echo "🔧 解决方案: 检查app_id和app_secret是否正确"
            ;;
        99991401)
            echo "🔧 解决方案: 配置IP白名单，添加当前IP到飞书应用设置"
            ;;
        *)
            echo "🔧 解决方案: 检查网络连接和应用状态"
            ;;
    esac
    exit 1
fi

# 步骤2: 测试应用信息获取
echo "🧪 步骤2: 测试应用信息获取"
app_info_response=$(curl -s -w "HTTP_CODE:%{http_code}" -X GET "$BASE_URL/open-apis/application/v6/applications/$APP_ID" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

app_info_http_code=$(echo "$app_info_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
app_info_body=$(echo "$app_info_response" | sed 's/HTTP_CODE:[0-9]*$//')

echo "应用信息HTTP状态码: $app_info_http_code"
echo "应用信息响应: $app_info_body"

if [ "$app_info_http_code" = "200" ] && echo "$app_info_body" | grep -q '"code":0'; then
    echo "✅ 应用信息获取成功，令牌有效"
else
    echo "⚠️ 应用信息获取失败，可能是权限问题"
fi
echo ""

# 步骤3: 测试多维表格应用信息
echo "📊 步骤3: 测试多维表格应用信息"
bitable_app_response=$(curl -s -w "HTTP_CODE:%{http_code}" -X GET "$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

bitable_http_code=$(echo "$bitable_app_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
bitable_body=$(echo "$bitable_app_response" | sed 's/HTTP_CODE:[0-9]*$//')

echo "多维表格应用HTTP状态码: $bitable_http_code"
echo "多维表格应用响应: $bitable_body"

if [ "$bitable_http_code" = "200" ] && echo "$bitable_body" | grep -q '"code":0'; then
    echo "✅ 多维表格应用信息获取成功"
    
    # 提取应用名称
    app_name=$(echo "$bitable_body" | sed -n 's/.*"name":"\([^"]*\)".*/\1/p')
    echo "多维表格名称: $app_name"
    
else
    echo "❌ 多维表格应用信息获取失败"
    error_code=$(echo "$bitable_body" | sed -n 's/.*"code":\([0-9]*\).*/\1/p')
    error_msg=$(echo "$bitable_body" | sed -n 's/.*"msg":"\([^"]*\)".*/\1/p')
    echo "错误码: $error_code"
    echo "错误信息: $error_msg"
    
    case $error_code in
        91402)
            echo "🔧 解决方案: 多维表格不存在或应用无权限访问"
            echo "   - 检查app_token是否正确"
            echo "   - 确保应用已安装到多维表格所在组织"
            echo "   - 检查应用权限配置"
            ;;
        99991401)
            echo "🔧 解决方案: IP访问被拒绝，需要配置IP白名单"
            ;;
        99991664)
            echo "🔧 解决方案: 应用权限不足，需要添加多维表格权限"
            ;;
        *)
            echo "🔧 解决方案: 检查应用配置和权限"
            ;;
    esac
    echo ""
    echo "⚠️ 由于多维表格应用信息获取失败，跳过后续测试"
    exit 1
fi
echo ""

# 步骤4: 获取数据表列表
echo "📋 步骤4: 获取数据表列表"
tables_response=$(curl -s -w "HTTP_CODE:%{http_code}" -X GET "$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN/tables" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

tables_http_code=$(echo "$tables_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
tables_body=$(echo "$tables_response" | sed 's/HTTP_CODE:[0-9]*$//')

echo "数据表列表HTTP状态码: $tables_http_code"
echo "数据表列表响应: $tables_body"

if [ "$tables_http_code" = "200" ] && echo "$tables_body" | grep -q '"code":0'; then
    echo "✅ 数据表列表获取成功"
    
    # 检查目标表是否存在
    if echo "$tables_body" | grep -q "\"table_id\":\"$TABLE_ID\""; then
        echo "✅ 找到目标数据表: $TABLE_ID"
    else
        echo "❌ 未找到目标数据表: $TABLE_ID"
        echo "可用的数据表:"
        echo "$tables_body" | sed -n 's/.*"table_id":"\([^"]*\)".*/\1/gp' | head -5
        exit 1
    fi
else
    echo "❌ 数据表列表获取失败"
    exit 1
fi
echo ""

# 步骤5: 测试获取记录
echo "📝 步骤5: 测试获取记录"
records_response=$(curl -s -w "HTTP_CODE:%{http_code}" -X GET "$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN/tables/$TABLE_ID/records?page_size=3" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

records_http_code=$(echo "$records_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
records_body=$(echo "$records_response" | sed 's/HTTP_CODE:[0-9]*$//')

echo "记录获取HTTP状态码: $records_http_code"
echo "记录获取响应: $records_body"

if [ "$records_http_code" = "200" ] && echo "$records_body" | grep -q '"code":0'; then
    echo "✅ 记录获取成功"
    
    # 分析字段
    echo ""
    echo "🔍 步骤6: 分析字段（查找图片字段）"
    
    if echo "$records_body" | grep -q '"file_token"'; then
        echo "✅ 发现图片字段！记录中包含文件附件"
        
        # 提取字段名
        echo "图片字段信息:"
        echo "$records_body" | grep -o '"[^"]*":\[{"file_token"' | sed 's/:\[{"file_token"//' | sed 's/"//g' | head -3
        
    else
        echo "⚠️ 未发现明显的图片字段"
        echo "所有字段名:"
        echo "$records_body" | grep -o '"[^"]*":' | sed 's/://g' | sed 's/"//g' | grep -v -E '^(record_id|created_time|created_by|last_modified_time|last_modified_by|fields)$' | head -10
    fi
    
else
    echo "❌ 记录获取失败"
    error_code=$(echo "$records_body" | sed -n 's/.*"code":\([0-9]*\).*/\1/p')
    error_msg=$(echo "$records_body" | sed -n 's/.*"msg":"\([^"]*\)".*/\1/p')
    echo "错误码: $error_code"
    echo "错误信息: $error_msg"
fi

echo ""
echo "🎉 调试完成！"
echo ""
echo "📝 总结:"
echo "1. 如果所有步骤都成功，说明配置正确，可以使用接口"
echo "2. 如果某个步骤失败，请根据错误信息进行相应配置"
echo "3. 最常见的问题是IP白名单和应用权限配置"
echo ""
echo "🔗 配置链接:"
echo "- 飞书开放平台: https://open.feishu.cn/"
echo "- 应用管理: https://open.feishu.cn/app"
echo "- 你的应用: https://open.feishu.cn/app/$APP_ID"
