<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片URL下载功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="url"], input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-urls {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .test-urls h3 {
            margin-top: 0;
            color: #495057;
        }
        .test-url {
            display: block;
            color: #007bff;
            text-decoration: none;
            margin: 5px 0;
            cursor: pointer;
        }
        .test-url:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 图片URL下载功能测试</h1>
        
        <div class="test-urls">
            <h3>测试用图片URL（点击使用）：</h3>
            <a class="test-url" onclick="setUrl('https://httpbin.org/image/jpeg')">https://httpbin.org/image/jpeg</a>
            <a class="test-url" onclick="setUrl('https://httpbin.org/image/png')">https://httpbin.org/image/png</a>
            <a class="test-url" onclick="setUrl('https://httpbin.org/image/webp')">https://httpbin.org/image/webp</a>
            <a class="test-url" onclick="setUrl('https://picsum.photos/400/300.jpg')">https://picsum.photos/400/300.jpg</a>
        </div>

        <form id="downloadForm">
            <div class="form-group">
                <label for="imageUrl">图片URL地址：</label>
                <input type="url" id="imageUrl" name="imageUrl" 
                       placeholder="请输入图片URL，例如：https://example.com/image.jpg" required>
            </div>
            
            <div class="form-group">
                <label for="customFileName">自定义文件名（可选）：</label>
                <input type="text" id="customFileName" name="customFileName" 
                       placeholder="例如：my_image（不需要扩展名）">
            </div>
            
            <button type="button" onclick="downloadImage('form')" id="formBtn">表单方式下载</button>
            <button type="button" onclick="downloadImage('json')" id="jsonBtn">JSON方式下载</button>
        </form>

        <div id="result"></div>
    </div>

    <script>
        function setUrl(url) {
            document.getElementById('imageUrl').value = url;
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + type;
            resultDiv.textContent = message;
        }

        function setLoading(loading) {
            const formBtn = document.getElementById('formBtn');
            const jsonBtn = document.getElementById('jsonBtn');
            formBtn.disabled = loading;
            jsonBtn.disabled = loading;
            
            if (loading) {
                showResult('正在下载图片，请稍候...', 'loading');
            }
        }

        async function downloadImage(method) {
            const imageUrl = document.getElementById('imageUrl').value;
            const customFileName = document.getElementById('customFileName').value;
            
            if (!imageUrl) {
                showResult('请输入图片URL地址', 'error');
                return;
            }

            setLoading(true);

            try {
                let response;
                
                if (method === 'form') {
                    // 表单方式
                    const formData = new FormData();
                    formData.append('imageUrl', imageUrl);
                    
                    response = await fetch('/api/file/download-from-url', {
                        method: 'POST',
                        body: formData
                    });
                } else {
                    // JSON方式
                    const requestBody = {
                        imageUrl: imageUrl
                    };
                    
                    if (customFileName) {
                        requestBody.customFileName = customFileName;
                    }
                    
                    response = await fetch('/api/file/download-image', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestBody)
                    });
                }

                const result = await response.json();
                
                if (result.success) {
                    const message = `✅ 下载成功！\n\n` +
                        `原始文件名: ${result.data.originalFileName}\n` +
                        `存储文件名: ${result.data.fileName}\n` +
                        `文件大小: ${(result.data.fileSize / 1024).toFixed(2)} KB\n` +
                        `文件类型: ${result.data.contentType}\n` +
                        `访问URL: ${result.data.fileUrl}\n` +
                        `上传时间: ${result.data.uploadTime}`;
                    showResult(message, 'success');
                } else {
                    showResult(`❌ 下载失败：${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 请求失败：${error.message}`, 'error');
            } finally {
                setLoading(false);
            }
        }
    </script>
</body>
</html>
