#!/bin/bash

# 测试图片下载修复效果的脚本

echo "=== 飞书图片下载修复测试 ==="
echo ""

# 服务器配置
SERVER_URL="http://localhost:8080"
API_ENDPOINT="/api/feishu/bitable/images/upload-to-local"

# 测试参数
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo "🔧 测试配置:"
echo "服务器地址: $SERVER_URL"
echo "App Token: $APP_TOKEN"
echo "Table ID: $TABLE_ID"
echo "View ID: $VIEW_ID"
echo ""

# 检查服务器状态
echo "1️⃣ 检查服务器状态..."
health_response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "$SERVER_URL/api/clothing/health" 2>/dev/null)
http_code=$(echo "$health_response" | grep "HTTP_CODE:" | cut -d: -f2)

if [ "$http_code" = "200" ]; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行或无法访问 (HTTP $http_code)"
    echo ""
    echo "🚀 启动服务器的方法:"
    echo "1. 使用Maven: mvn spring-boot:run"
    echo "2. 使用jar包: java -jar target/ziniao-ai-demo-1.0.0.jar"
    echo "3. 使用IDE: 运行 ZiniaoAiDemoApplication.main()"
    echo ""
    echo "请先启动服务器，然后重新运行此测试脚本"
    exit 1
fi
echo ""

# 测试1: 小批量图片下载（避免超时）
echo "2️⃣ 测试小批量图片下载..."

request_body_small=$(cat <<EOF
{
    "appToken": "$APP_TOKEN",
    "tableId": "$TABLE_ID",
    "viewId": "$VIEW_ID",
    "downloadToLocal": true,
    "pageSize": 2,
    "includeImageDetails": true,
    "downloadTimeout": 60,
    "maxConcurrentDownloads": 1
}
EOF
)

echo "请求体（小批量）:"
echo "$request_body_small" | jq . 2>/dev/null || echo "$request_body_small"
echo ""

echo "发送请求..."
response_small=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST "$SERVER_URL$API_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$request_body_small" \
    --max-time 90 2>/dev/null)

http_code_small=$(echo "$response_small" | grep "HTTP_CODE:" | cut -d: -f2)
response_body_small=$(echo "$response_small" | sed '/HTTP_CODE:/d')

echo "响应状态码: $http_code_small"
echo "响应内容:"
echo "$response_body_small" | jq . 2>/dev/null || echo "$response_body_small"
echo ""

# 分析响应
if [ "$http_code_small" = "200" ]; then
    echo "✅ 请求成功！"
    
    # 尝试解析成功和失败的图片数量
    if command -v jq &> /dev/null; then
        total_images=$(echo "$response_body_small" | jq -r '.data.totalImages // "N/A"')
        successful_downloads=$(echo "$response_body_small" | jq -r '.data.successfulDownloads // "N/A"')
        failed_downloads=$(echo "$response_body_small" | jq -r '.data.failedDownloads // "N/A"')
        
        echo "📊 处理结果:"
        echo "  总图片数: $total_images"
        echo "  成功下载: $successful_downloads"
        echo "  失败数量: $failed_downloads"
        
        if [ "$successful_downloads" != "0" ] && [ "$successful_downloads" != "N/A" ]; then
            echo "🎉 图片下载修复成功！有图片成功下载到本地"
            
            # 检查上传目录
            if [ -d "uploads" ]; then
                echo ""
                echo "📁 检查上传目录:"
                find uploads -name "*.jpg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp" | head -5 | while read file; do
                    echo "  ✓ $file"
                done
            fi
        elif [ "$failed_downloads" = "$total_images" ] && [ "$total_images" != "0" ]; then
            echo "⚠️ 所有图片下载失败，需要检查日志"
            echo ""
            echo "🔍 可能的问题:"
            echo "1. batch_get_tmp_download_url API调用失败"
            echo "2. 真实下载链接获取失败"
            echo "3. 图片文件保存失败"
            echo ""
            echo "📋 检查日志:"
            echo "tail -f logs/ziniao-api.log | grep -E '(getRealDownloadUrl|batch_get_tmp_download_url|saveImageFromConnection)'"
        else
            echo "ℹ️ 没有找到图片或图片数量为0"
        fi
        
    fi
    
elif [ "$http_code_small" = "400" ]; then
    echo "❌ 请求参数错误 (400)"
    echo "检查appToken、tableId、viewId是否正确"
    
elif [ "$http_code_small" = "401" ]; then
    echo "❌ 认证失败 (401)"
    echo "检查飞书应用配置和权限"
    
elif [ "$http_code_small" = "500" ]; then
    echo "❌ 服务器内部错误 (500)"
    echo "检查服务器日志获取详细错误信息"
    
else
    echo "❌ 未知错误 (HTTP $http_code_small)"
fi

echo ""

# 测试2: 检查修复前后的差异
echo "3️⃣ 检查修复效果..."

echo "🔍 修复前的问题:"
echo "1. ❌ Content-Type: application/json (batch_get_tmp_download_url返回JSON)"
echo "2. ❌ URL指向的不是图片文件"
echo "3. ❌ 99991661 Missing access token错误"
echo ""

echo "✅ 修复后的改进:"
echo "1. ✅ 自动调用batch_get_tmp_download_url获取真实下载链接"
echo "2. ✅ 正确处理JSON响应，提取实际图片URL"
echo "3. ✅ 完整的token认证和重试机制"
echo "4. ✅ 避免重复请求导致的400错误"
echo ""

# 总结
echo "🎯 测试总结:"
echo "================================"

if [ "$http_code_small" = "200" ]; then
    if command -v jq &> /dev/null; then
        successful_downloads=$(echo "$response_body_small" | jq -r '.data.successfulDownloads // "0"')
        if [ "$successful_downloads" != "0" ]; then
            echo "🎉 修复成功！图片可以正常下载"
        else
            echo "⚠️ 修复部分成功，但仍有问题需要解决"
        fi
    else
        echo "✅ 接口调用成功，需要检查具体下载结果"
    fi
else
    echo "❌ 接口调用失败，需要进一步排查"
fi

echo ""
echo "📝 下一步建议:"
echo "1. 查看服务器日志了解详细处理过程"
echo "2. 检查uploads目录确认文件是否成功保存"
echo "3. 如果仍有问题，检查飞书API权限配置"
echo ""
echo "🔗 相关命令:"
echo "- 查看日志: tail -f logs/ziniao-api.log"
echo "- 检查上传: ls -la uploads/"
echo "- 健康检查: curl http://localhost:8080/api/clothing/health"
echo ""
echo "测试完成！"
