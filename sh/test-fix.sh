#!/bin/bash

echo "=== 测试修复后的 /api/clothing/fittingRoom 接口 ==="

curl -X POST "http://localhost:18088/api/clothing/fittingRoom" \
  -H "Content-Type: application/json" \
  -d '{
    "upperOriginUrl": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
    "upperImageUrl": "",
    "downOriginUrl": "https://cdn.linkfox.com/ai-site/test/workbench/down-true3.jpg",
    "downImageUrl": "",
    "modelImageUrl": "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png",
    "modelMaskImageUrl": "",
    "outputNum": 1
  }'

echo ""
echo "=== 测试完成 ==="
