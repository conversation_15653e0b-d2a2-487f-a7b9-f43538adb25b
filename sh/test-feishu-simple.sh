#!/bin/bash

# 简单的飞书多维表格测试脚本
# 直接调用飞书API，验证配置和权限

echo "=== 飞书多维表格简单测试 ==="
echo ""

# 你的飞书应用配置
APP_ID="cli_a8fe3e73bd78d00d"
APP_SECRET="whPto88DEdJ9n3uBiDoDNhlTEb3KAJLU"
BASE_URL="https://open.feishu.cn"

# 你的多维表格参数
APP_TOKEN="Wc2WwTiksil7vVkE1hqcmCmmneb"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo "📋 配置信息:"
echo "  飞书App ID: $APP_ID"
echo "  多维表格Token: $APP_TOKEN"
echo "  数据表ID: $TABLE_ID"
echo "  视图ID: $VIEW_ID"
echo ""

# 1. 获取应用访问令牌
echo "🔑 1. 获取飞书应用访问令牌"
token_response=$(curl -s -X POST "$BASE_URL/open-apis/auth/v3/app_access_token/internal" \
  -H "Content-Type: application/json" \
  -d "{
    \"app_id\": \"$APP_ID\",
    \"app_secret\": \"$APP_SECRET\"
  }")

echo "令牌响应: $token_response"

# 检查是否成功获取令牌
if echo "$token_response" | grep -q '"code":0'; then
    echo "✅ 令牌获取成功"
    
    # 提取令牌
    app_access_token=$(echo "$token_response" | sed -n 's/.*"app_access_token":"\([^"]*\)".*/\1/p')
    echo "令牌: ${app_access_token:0:20}..."
    echo ""
    
    # 2. 测试获取多维表格记录
    echo "📊 2. 测试获取多维表格记录"
    records_url="$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN/tables/$TABLE_ID/records?page_size=5"
    echo "请求URL: $records_url"
    
    records_response=$(curl -s -X GET "$records_url" \
      -H "Authorization: Bearer $app_access_token" \
      -H "Content-Type: application/json")
    
    echo "记录响应: $records_response"
    echo ""
    
    # 检查响应
    if echo "$records_response" | grep -q '"code":0'; then
        echo "✅ 多维表格记录获取成功"
        
        # 分析记录中的字段
        echo ""
        echo "🔍 3. 分析记录字段（查找图片字段）"
        
        # 提取字段信息（简单分析）
        if echo "$records_response" | grep -q '"fields"'; then
            echo "找到记录字段，正在分析..."
            
            # 查找可能的图片字段
            if echo "$records_response" | grep -q '"file_token"'; then
                echo "✅ 发现图片字段！记录中包含文件附件"
            else
                echo "⚠️  未发现明显的图片字段，可能需要检查字段名称"
            fi
            
            # 显示字段名称（如果可以提取）
            echo ""
            echo "📋 记录字段分析:"
            echo "$records_response" | sed -n 's/.*"fields":\({[^}]*}\).*/\1/p' | head -3
            
        else
            echo "❌ 未找到记录字段信息"
        fi
        
    elif echo "$records_response" | grep -q '"code":99991401'; then
        echo "❌ IP访问被拒绝，需要在飞书开放平台配置IP白名单"
        echo "当前IP可能需要添加到应用的IP白名单中"
        
    elif echo "$records_response" | grep -q '"code":99991664'; then
        echo "❌ 应用权限不足，需要添加多维表格相关权限"
        echo "请在飞书开放平台为应用添加以下权限："
        echo "- bitable:app"
        echo "- bitable:app:readonly"
        
    else
        echo "❌ 多维表格记录获取失败"
        echo "可能的原因："
        echo "- app_token 不正确"
        echo "- table_id 不正确"
        echo "- 应用没有访问该表格的权限"
    fi
    
else
    echo "❌ 令牌获取失败"
    
    if echo "$token_response" | grep -q '"code":10003'; then
        echo "错误: 参数无效 (invalid param)"
        echo "请检查 app_id 和 app_secret 是否正确"
    elif echo "$token_response" | grep -q '"code":99991401'; then
        echo "错误: IP访问被拒绝"
        echo "请在飞书开放平台配置IP白名单"
    else
        echo "其他错误，请检查网络连接和应用配置"
    fi
fi

echo ""
echo "📝 总结:"
echo "1. 如果令牌获取成功但表格访问失败，通常是权限问题"
echo "2. 如果出现IP限制错误，需要在飞书开放平台配置IP白名单"
echo "3. 如果一切正常，说明你的配置正确，可以开始使用接口"
echo ""
echo "🔗 相关链接:"
echo "- 飞书开放平台: https://open.feishu.cn/"
echo "- 应用管理: https://open.feishu.cn/app"
echo "- 权限配置: 应用详情 -> 权限管理"
echo "- IP白名单: 应用详情 -> 安全设置"
