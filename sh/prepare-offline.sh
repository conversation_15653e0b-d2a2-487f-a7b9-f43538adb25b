#!/bin/bash

# 为离线Docker构建准备项目
# 此脚本确保所有依赖都可用于离线操作

set -e  # 遇到错误时退出

echo "=== 准备紫鸟AI演示离线Docker构建 ==="
echo ""

# 输出颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

print_status() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查必需文件
print_status "检查必需文件..."

if [ ! -f "sdk-java-5.0.6.jar" ]; then
    print_error "未找到sdk-java-5.0.6.jar。此文件是应用程序必需的。"
    exit 1
fi

if [ ! -f "pom.xml" ]; then
    print_warning "未找到pom.xml。创建最小配置..."
    cat > pom.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ziniao</groupId>
    <artifactId>ziniao-ai-demo</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    <properties>
        <java.version>8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
</project>
EOF
fi

print_success "必需文件检查完成"

# 创建lib目录并下载依赖
print_status "准备离线操作的依赖..."

mkdir -p lib

# 必需依赖列表
deps_urls=(
    "https://repo1.maven.org/maven2/com/squareup/okhttp3/okhttp/4.9.3/okhttp-4.9.3.jar"
    "https://repo1.maven.org/maven2/com/squareup/okhttp3/okhttp-sse/4.9.3/okhttp-sse-4.9.3.jar"
    "https://repo1.maven.org/maven2/com/squareup/okhttp3/logging-interceptor/4.9.3/logging-interceptor-4.9.3.jar"
    "https://repo1.maven.org/maven2/com/squareup/okio/okio/2.8.0/okio-2.8.0.jar"
    "https://repo1.maven.org/maven2/org/jetbrains/kotlin/kotlin-stdlib/1.4.10/kotlin-stdlib-1.4.10.jar"
    "https://repo1.maven.org/maven2/org/jetbrains/kotlin/kotlin-stdlib-common/1.4.10/kotlin-stdlib-common-1.4.10.jar"
    "https://repo1.maven.org/maven2/org/jetbrains/annotations/13.0/annotations-13.0.jar"
)

for url in "${deps_urls[@]}"; do
    filename=$(basename "$url")
    if [ ! -f "lib/$filename" ]; then
        print_status "正在下载 $filename..."
        if curl -L --connect-timeout 10 --max-time 30 -o "lib/$filename" "$url"; then
            print_success "$filename 下载成功"
        else
            print_error "下载 $filename 失败"
            exit 1
        fi
    else
        print_success "$filename 已存在"
    fi
done

# 创建最小源码结构（如果不存在）
if [ ! -d "src" ]; then
    print_status "创建最小源码结构..."
    mkdir -p src/main/java/com/ziniao
    mkdir -p src/main/resources
    
    # 创建最小Spring Boot应用
    cat > src/main/java/com/ziniao/ZiniaoAiDemoApplication.java << 'EOF'
package com.ziniao;

public class ZiniaoAiDemoApplication {
    public static void main(String[] args) {
        System.out.println("紫鸟AI演示应用");
        System.out.println("这是Docker容器的最小化版本");
        
        // 保持应用运行
        try {
            Thread.sleep(Long.MAX_VALUE);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
EOF

    # 创建最小application.yml
    cat > src/main/resources/application.yml << 'EOF'
server:
  port: 8080

logging:
  level:
    com.ziniao: INFO
    root: WARN

ziniao:
  api:
    app-id: ${ZINIAO_API_APP_ID:your_app_id_here}
    private-key: ${ZINIAO_API_PRIVATE_KEY:your_private_key_here}
    base-url: ${ZINIAO_API_BASE_URL:https://sbappstoreapi.ziniao.com}
EOF

    print_success "最小源码结构已创建"
fi

# 创建日志目录
mkdir -p logs

print_success "项目已准备好进行离线Docker构建！"
echo ""
print_status "下一步："
echo "  1. 构建Docker镜像: ./docker-build.sh"
echo "  2. 运行容器: ./docker-run.sh"
echo "  3. 或使用Docker Compose: docker-compose up -d"
echo ""
print_status "确保为Docker Compose在.env文件中配置您的API凭据"
