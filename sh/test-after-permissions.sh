#!/bin/bash

# 权限配置后的验证测试

echo "=== 权限配置后验证测试 ==="
echo ""

echo "📋 测试参数:"
echo "  App Token: Wc2WwTiksil7vVkE1hqcmCmmneb"
echo "  Table ID: tbl4sH8PYHUk36K0"
echo "  View ID: vewgI30A6c"
echo ""

echo "🧪 测试1: 检查权限状态"
./check-permissions-status.sh
echo ""

echo "🧪 测试2: 获取多维表格记录"
curl -X POST "http://localhost:8080/api/feishu/bitable/records" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "Wc2WwTiksil7vVkE1hqcmCmmneb",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "pageSize": 3
  }' | jq '.' 2>/dev/null || cat

echo ""
echo ""

echo "🧪 测试3: 获取多维表格图片（简化接口）"
curl -X POST "http://localhost:8080/api/feishu/bitable/images/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "Wc2WwTiksil7vVkE1hqcmCmmneb",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "downloadToLocal": true,
    "pageSize": 5,
    "includeImageDetails": true
  }' | jq '.' 2>/dev/null || cat

echo ""
echo ""

echo "📝 如果测试仍然失败:"
echo "1. 确认权限已发布（不只是保存）"
echo "2. 等待更长时间（最多30分钟）"
echo "3. 检查应用是否安装到正确的飞书组织"
echo "4. 联系飞书管理员确认权限审批状态"
echo ""
echo "🔗 权限配置链接:"
echo "https://open.feishu.cn/app/cli_a8fe3e73bd78d00d"
