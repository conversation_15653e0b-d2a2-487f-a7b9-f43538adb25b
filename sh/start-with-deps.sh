#!/bin/bash

# 带完整依赖的启动脚本

echo "=== 紫鸟AI穿衣API 启动（包含所有依赖） ==="

# 检查必要文件
if [ ! -f "sdk-java-5.0.6.jar" ]; then
    echo "❌ 找不到 sdk-java-5.0.6.jar"
    exit 1
fi

if [ ! -d "target/classes" ]; then
    echo "❌ 找不到编译输出目录"
    exit 1
fi

if [ ! -d "lib" ] || [ ! "$(ls -A lib)" ]; then
    echo "❌ 找不到依赖文件，请先运行: ./download-deps.sh"
    exit 1
fi

echo "✅ 检查通过，开始启动应用..."

# 构建完整的classpath
CLASSPATH="target/classes:sdk-java-5.0.6.jar:lib/*"

echo "Classpath: $CLASSPATH"
echo ""

# 启动应用
java -cp "$CLASSPATH" com.ziniao.ZiniaoAiDemoApplication

echo ""
echo "应用已停止"
