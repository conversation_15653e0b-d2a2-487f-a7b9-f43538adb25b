#!/bin/bash

# 飞书多维表格API测试脚本
# 使用方法: ./test-feishu-bitable.sh

echo "=== 飞书多维表格API测试脚本 ==="
echo ""

# 服务器地址
BASE_URL="http://localhost:8080"

# 测试参数（请根据实际情况修改）
APP_TOKEN="your_app_token_here"
TABLE_ID="your_table_id_here"
RECORD_ID="your_record_id_here"

echo "📋 测试参数:"
echo "  服务器地址: $BASE_URL"
echo "  App Token: $APP_TOKEN"
echo "  Table ID: $TABLE_ID"
echo "  Record ID: $RECORD_ID"
echo ""

# 1. 健康检查
echo "🔍 1. 健康检查"
echo "GET $BASE_URL/api/feishu/bitable/health"
curl -X GET "$BASE_URL/api/feishu/bitable/health" \
  -H "Content-Type: application/json" \
  -w "\n状态码: %{http_code}\n" \
  -s
echo ""

# 2. 获取服务信息
echo "ℹ️  2. 获取服务信息"
echo "GET $BASE_URL/api/feishu/bitable/info"
curl -X GET "$BASE_URL/api/feishu/bitable/info" \
  -H "Content-Type: application/json" \
  -w "\n状态码: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || cat
echo ""

# 3. 获取多维表格记录
echo "📊 3. 获取多维表格记录"
echo "POST $BASE_URL/api/feishu/bitable/records"
curl -X POST "$BASE_URL/api/feishu/bitable/records" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"pageSize\": 10
  }" \
  -w "\n状态码: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || cat
echo ""

# 4. 获取多维表格图片
echo "🖼️  4. 获取多维表格图片"
echo "POST $BASE_URL/api/feishu/bitable/images"
curl -X POST "$BASE_URL/api/feishu/bitable/images" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"downloadToLocal\": true,
    \"pageSize\": 5,
    \"includeImageDetails\": true
  }" \
  -w "\n状态码: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || cat
echo ""

# 5. 获取指定记录的图片
echo "🎯 5. 获取指定记录的图片"
echo "POST $BASE_URL/api/feishu/bitable/images/record/$RECORD_ID"
curl -X POST "$BASE_URL/api/feishu/bitable/images/record/$RECORD_ID" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"downloadToLocal\": true,
    \"includeImageDetails\": true
  }" \
  -w "\n状态码: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || cat
echo ""

# 6. 测试带筛选条件的查询
echo "🔍 6. 测试带筛选条件的查询"
echo "POST $BASE_URL/api/feishu/bitable/images"
curl -X POST "$BASE_URL/api/feishu/bitable/images" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"downloadToLocal\": false,
    \"pageSize\": 3,
    \"imageFields\": [\"图片\", \"头像\", \"封面\"]
  }" \
  -w "\n状态码: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || cat
echo ""

echo "✅ 测试完成！"
echo ""
echo "📝 使用说明:"
echo "1. 请先在 application.yml 中配置正确的飞书应用信息"
echo "2. 修改脚本中的 APP_TOKEN、TABLE_ID、RECORD_ID 为实际值"
echo "3. 确保飞书应用有访问对应多维表格的权限"
echo "4. 查看 Swagger 文档: http://localhost:8080/doc.html"
echo ""
echo "🔧 故障排除:"
echo "- 如果返回 401 错误，检查飞书应用配置"
echo "- 如果返回 403 错误，检查应用权限"
echo "- 如果返回 404 错误，检查 app_token 和 table_id"
echo "- 查看日志文件: logs/ziniao-ai-demo.log"
