#!/bin/bash

# 简单启动脚本

echo "=== 紫鸟AI穿衣API 简单启动 ==="

# 检查必要文件
if [ ! -f "sdk-java-5.0.6.jar" ]; then
    echo "❌ 找不到 sdk-java-5.0.6.jar"
    exit 1
fi

if [ ! -d "target/classes" ]; then
    echo "❌ 找不到编译输出目录，请先编译项目"
    exit 1
fi

# 创建lib目录
mkdir -p lib

# 下载关键依赖
echo "检查并下载必要依赖..."

# OkHttp
if [ ! -f "lib/okhttp-4.9.3.jar" ]; then
    echo "下载 OkHttp..."
    curl -L -o "lib/okhttp-4.9.3.jar" "https://repo1.maven.org/maven2/com/squareup/okhttp3/okhttp/4.9.3/okhttp-4.9.3.jar" &
fi

# OkIO
if [ ! -f "lib/okio-2.8.0.jar" ]; then
    echo "下载 OkIO..."
    curl -L -o "lib/okio-2.8.0.jar" "https://repo1.maven.org/maven2/com/squareup/okio/okio/2.8.0/okio-2.8.0.jar" &
fi

# Kotlin stdlib
if [ ! -f "lib/kotlin-stdlib-1.4.10.jar" ]; then
    echo "下载 Kotlin stdlib..."
    curl -L -o "lib/kotlin-stdlib-1.4.10.jar" "https://repo1.maven.org/maven2/org/jetbrains/kotlin/kotlin-stdlib/1.4.10/kotlin-stdlib-1.4.10.jar" &
fi

# 等待下载完成
wait

echo "依赖检查完成"

# 构建classpath
CLASSPATH="target/classes:sdk-java-5.0.6.jar"
if [ -d "lib" ] && [ "$(ls -A lib)" ]; then
    CLASSPATH="$CLASSPATH:lib/*"
fi

echo "启动应用..."
echo "Classpath: $CLASSPATH"

# 启动应用
java -cp "$CLASSPATH" com.ziniao.ZiniaoAiDemoSimpleApplication

echo "应用已停止"
