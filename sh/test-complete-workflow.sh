#!/bin/bash

# 完整的AI穿衣工作流程测试

echo "=== 紫鸟AI穿衣完整工作流程测试 ==="

BASE_URL="http://localhost:8080"

echo ""
echo "🚀 步骤1: 提交AI穿衣任务"

CLOTHING_REQUEST='{
  "personImage": "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png",
  "clothingImage": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
  "clothingType": "上衣"
}'

echo "请求参数: $CLOTHING_REQUEST"
echo ""

SUBMIT_RESPONSE=$(curl -s -X POST "$BASE_URL/api/clothing/process" \
  -H "Content-Type: application/json" \
  -d "$CLOTHING_REQUEST")

echo "提交响应: $SUBMIT_RESPONSE"

# 检查提交是否成功
if echo "$SUBMIT_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 任务提交成功！"
    
    # 提取任务ID
    TASK_ID=$(echo "$SUBMIT_RESPONSE" | grep -o '"taskId":"[^"]*"' | cut -d'"' -f4)
    
    if [ ! -z "$TASK_ID" ]; then
        echo "📋 任务ID: $TASK_ID"
        echo ""
        
        echo "🔍 步骤2: 轮询查询任务结果"
        echo "使用官方接口: /linkfox-ai/image/v2/make/info"
        echo ""
        
        MAX_ATTEMPTS=12  # 最多查询12次 (1分钟)
        ATTEMPT=0
        
        while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
            echo "第 $((ATTEMPT+1)) 次查询 ($(date '+%H:%M:%S'))..."
            
            RESULT_RESPONSE=$(curl -s "$BASE_URL/api/clothing/result/$TASK_ID")
            echo "查询响应: $RESULT_RESPONSE"
            
            if echo "$RESULT_RESPONSE" | grep -q '"code":200'; then
                STATUS=$(echo "$RESULT_RESPONSE" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
                PROGRESS=$(echo "$RESULT_RESPONSE" | grep -o '"progress":[0-9]*' | cut -d':' -f2)
                
                echo "📊 状态: $STATUS, 进度: $PROGRESS%"
                
                if [ "$STATUS" = "completed" ]; then
                    echo "🎉 任务完成！"
                    
                    # 尝试提取结果图片URL
                    RESULT_IMAGE=$(echo "$RESULT_RESPONSE" | grep -o '"resultImage":"[^"]*"' | cut -d'"' -f4)
                    if [ ! -z "$RESULT_IMAGE" ]; then
                        echo "🖼️  结果图片: $RESULT_IMAGE"
                        echo ""
                        echo "⚠️  重要提醒:"
                        echo "   - 图片地址有效期只有8小时"
                        echo "   - 请及时下载保存到您的存储服务中"
                        echo "   - 如果过期，可重新调用结果查询接口刷新地址"
                    else
                        echo "⚠️  任务完成但未找到结果图片URL"
                    fi
                    break
                    
                elif [ "$STATUS" = "failed" ]; then
                    echo "❌ 任务失败"
                    break
                    
                elif [ "$STATUS" = "queuing" ]; then
                    echo "⏳ 任务排队中..."
                    
                elif [ "$STATUS" = "processing" ]; then
                    echo "🔄 任务生成中..."
                    
                else
                    echo "📝 任务状态: $STATUS"
                fi
            else
                echo "❌ 查询请求失败"
                echo "响应: $RESULT_RESPONSE"
            fi
            
            echo ""
            
            # 如果任务已完成或失败，退出循环
            if [ "$STATUS" = "completed" ] || [ "$STATUS" = "failed" ]; then
                break
            fi
            
            # 等待5秒后继续查询
            echo "⏱️  等待5秒后继续查询..."
            sleep 5
            ATTEMPT=$((ATTEMPT+1))
        done
        
        if [ $ATTEMPT -eq $MAX_ATTEMPTS ] && [ "$STATUS" != "completed" ] && [ "$STATUS" != "failed" ]; then
            echo "⏰ 查询超时，任务可能仍在处理中"
            echo "您可以稍后手动查询: curl $BASE_URL/api/clothing/result/$TASK_ID"
        fi
        
    else
        echo "❌ 无法提取任务ID"
    fi
    
else
    echo "❌ 任务提交失败"
    echo "响应: $SUBMIT_RESPONSE"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "📚 更多信息:"
echo "- 完整工作流程文档: cat COMPLETE_WORKFLOW.md"
echo "- API文档: $BASE_URL/doc.html"
echo "- Swagger UI: $BASE_URL/swagger-ui/"
echo ""
echo "🔧 手动测试命令:"
echo "# 提交任务"
echo "curl -X POST \"$BASE_URL/api/clothing/process\" -H \"Content-Type: application/json\" -d '$CLOTHING_REQUEST'"
echo ""
echo "# 查询结果 (替换TASK_ID)"
echo "curl -X GET \"$BASE_URL/api/clothing/result/TASK_ID\""
