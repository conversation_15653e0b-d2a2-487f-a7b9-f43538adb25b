#!/bin/bash

# 你的飞书多维表格测试脚本
# 基于你提供的链接: https://lbit922efv.feishu.cn/wiki/Wc2WwTiksil7vVkE1hqcmCmmneb?table=tbl4sH8PYHUk36K0&view=vewgI30A6c

echo "=== 你的飞书多维表格图片获取测试 ==="
echo ""

# 服务器地址
BASE_URL="http://localhost:8080"

# 从你的链接提取的参数
APP_TOKEN="Wc2WwTiksil7vVkE1hqcmCmmneb"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo "📋 你的表格参数:"
echo "  App Token: $APP_TOKEN"
echo "  Table ID: $TABLE_ID"
echo "  View ID: $VIEW_ID"
echo ""

# 1. 健康检查
echo "🔍 1. 健康检查"
curl -X GET "$BASE_URL/api/feishu/bitable/health" \
  -H "Content-Type: application/json" \
  -w "\n状态码: %{http_code}\n" \
  -s
echo ""

# 2. 获取表格记录（先看看有什么数据）
echo "📊 2. 获取表格记录"
echo "POST $BASE_URL/api/feishu/bitable/records"
curl -X POST "$BASE_URL/api/feishu/bitable/records" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"viewId\": \"$VIEW_ID\",
    \"pageSize\": 5
  }" \
  -w "\n状态码: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || cat
echo ""

# 3. 使用简化接口获取所有图片并下载到本地（推荐）
echo "🖼️  3. 使用简化接口获取所有图片并下载到本地（推荐）"
echo "POST $BASE_URL/api/feishu/bitable/images/simple"
curl -X POST "$BASE_URL/api/feishu/bitable/images/simple" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"viewId\": \"$VIEW_ID\",
    \"downloadToLocal\": true,
    \"pageSize\": 10,
    \"includeImageDetails\": true
  }" \
  -w "\n状态码: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || cat
echo ""

# 4. 只获取图片URL不下载（使用简化接口）
echo "🔗 4. 只获取图片URL（不下载到本地）"
curl -X POST "$BASE_URL/api/feishu/bitable/images/simple" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"viewId\": \"$VIEW_ID\",
    \"downloadToLocal\": false,
    \"pageSize\": 5,
    \"includeImageDetails\": true
  }" \
  -w "\n状态码: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || cat
echo ""

# 5. 获取指定字段的图片（使用简化接口）
echo "🎯 5. 获取指定字段的图片"
curl -X POST "$BASE_URL/api/feishu/bitable/images/simple" \
  -H "Content-Type: application/json" \
  -d "{
    \"appToken\": \"$APP_TOKEN\",
    \"tableId\": \"$TABLE_ID\",
    \"viewId\": \"$VIEW_ID\",
    \"imageFields\": [\"图片\", \"头像\", \"封面\", \"照片\"],
    \"downloadToLocal\": true,
    \"pageSize\": 5
  }" \
  -w "\n状态码: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || cat
echo ""

echo "✅ 测试完成！"
echo ""
echo "📝 说明:"
echo "1. 你的飞书应用配置已更新："
echo "   - App ID: cli_a8fe3e73bd78d00d"
echo "   - App Secret: whPto88DEdJ9n3uBiDoDNhlTEb3KAJLU"
echo ""
echo "2. 从你的链接提取的参数："
echo "   - 链接: https://lbit922efv.feishu.cn/wiki/Wc2WwTiksil7vVkE1hqcmCmmneb?table=tbl4sH8PYHUk36K0&view=vewgI30A6c"
echo "   - App Token: $APP_TOKEN"
echo "   - Table ID: $TABLE_ID"
echo "   - View ID: $VIEW_ID"
echo "   - 类型: 知识库中的多维表格"
echo ""
echo "3. 如果遇到91402错误，需要配置权限："
echo "   - 访问: https://open.feishu.cn/app/cli_a8fe3e73bd78d00d"
echo "   - 添加权限: bitable:app, bitable:app:readonly, wiki:wiki:readonly"
echo "   - 发布权限并重新安装应用"
echo ""
echo "4. 权限配置指南: 查看 fix-permissions.md"
echo "5. 诊断工具: ./diagnose-bitable-access.sh"
echo "6. 下载的图片会保存在 uploads/ 目录下"
