#!/bin/bash

# 深度诊断飞书多维表格访问问题

echo "=== 飞书多维表格深度诊断 ==="
echo ""

APP_ID="cli_a8fe3e73bd78d00d"
APP_SECRET="whPto88DEdJ9n3uBiDoDNhlTEb3KAJLU"
BASE_URL="https://open.feishu.cn"
APP_TOKEN="Wc2WwTiksil7vVkE1hqcmCmmneb"
TABLE_ID="tbl4sH8PYHUk36K0"

echo "📋 配置信息:"
echo "  App ID: $APP_ID"
echo "  App Token: $APP_TOKEN"
echo "  Table ID: $TABLE_ID"
echo "  多维表格链接: https://lbit922efv.feishu.cn/wiki/$APP_TOKEN"
echo ""

# 获取令牌
echo "🔑 步骤1: 获取访问令牌"
token_response=$(curl -s -X POST "$BASE_URL/open-apis/auth/v3/app_access_token/internal" \
  -H "Content-Type: application/json" \
  -d "{\"app_id\": \"$APP_ID\", \"app_secret\": \"$APP_SECRET\"}")

if echo "$token_response" | grep -q '"code":0'; then
    app_access_token=$(echo "$token_response" | sed -n 's/.*"app_access_token":"\([^"]*\)".*/\1/p')
    echo "✅ 令牌获取成功: ${app_access_token:0:30}..."
else
    echo "❌ 令牌获取失败: $token_response"
    exit 1
fi

echo ""
echo "🔍 步骤2: 检查应用权限详情"
app_scopes_response=$(curl -s -X GET "$BASE_URL/open-apis/application/v6/applications/$APP_ID/app_scopes" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

echo "应用权限响应: $app_scopes_response"

if echo "$app_scopes_response" | grep -q "bitable"; then
    echo "✅ 发现多维表格权限"
else
    echo "❌ 未发现多维表格权限"
fi

echo ""
echo "🔍 步骤3: 测试不同的API端点"

# 测试1: 多维表格应用信息
echo "测试1: 多维表格应用信息"
bitable_app_response=$(curl -s -w "HTTP_CODE:%{http_code}" -X GET "$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

http_code=$(echo "$bitable_app_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
response_body=$(echo "$bitable_app_response" | sed 's/HTTP_CODE:[0-9]*$//')

echo "HTTP状态码: $http_code"
echo "响应内容: $response_body"

if [ "$http_code" = "200" ] && echo "$response_body" | grep -q '"code":0'; then
    echo "✅ 多维表格应用信息获取成功"
    
    # 提取应用名称
    app_name=$(echo "$response_body" | sed -n 's/.*"name":"\([^"]*\)".*/\1/p')
    echo "多维表格名称: $app_name"
    
    # 测试2: 获取表格列表
    echo ""
    echo "测试2: 获取表格列表"
    tables_response=$(curl -s -X GET "$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN/tables" \
      -H "Authorization: Bearer $app_access_token" \
      -H "Content-Type: application/json")
    
    echo "表格列表响应: $tables_response"
    
    if echo "$tables_response" | grep -q '"code":0'; then
        echo "✅ 表格列表获取成功"
        
        # 检查目标表格是否存在
        if echo "$tables_response" | grep -q "\"table_id\":\"$TABLE_ID\""; then
            echo "✅ 找到目标表格: $TABLE_ID"
            
            # 测试3: 获取表格记录
            echo ""
            echo "测试3: 获取表格记录"
            records_response=$(curl -s -X GET "$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN/tables/$TABLE_ID/records?page_size=1" \
              -H "Authorization: Bearer $app_access_token" \
              -H "Content-Type: application/json")
            
            echo "记录响应: $records_response"
            
            if echo "$records_response" | grep -q '"code":0'; then
                echo "✅ 记录获取成功！问题已解决"
                
                # 分析字段
                if echo "$records_response" | grep -q '"file_token"'; then
                    echo "✅ 发现图片字段"
                else
                    echo "⚠️ 未发现图片字段，但记录获取成功"
                fi
                
            else
                echo "❌ 记录获取失败"
                error_code=$(echo "$records_response" | sed -n 's/.*"code":\([0-9]*\).*/\1/p')
                echo "错误码: $error_code"
            fi
            
        else
            echo "❌ 未找到目标表格: $TABLE_ID"
            echo "可用的表格ID:"
            echo "$tables_response" | grep -o '"table_id":"[^"]*"' | sed 's/"table_id":"//g' | sed 's/"//g'
        fi
        
    else
        echo "❌ 表格列表获取失败"
        error_code=$(echo "$tables_response" | sed -n 's/.*"code":\([0-9]*\).*/\1/p')
        echo "错误码: $error_code"
    fi
    
else
    echo "❌ 多维表格应用信息获取失败"
    error_code=$(echo "$response_body" | sed -n 's/.*"code":\([0-9]*\).*/\1/p')
    error_msg=$(echo "$response_body" | sed -n 's/.*"msg":"\([^"]*\)".*/\1/p')
    echo "错误码: $error_code"
    echo "错误信息: $error_msg"
fi

echo ""
echo "🔍 步骤4: 检查知识库访问（备选方案）"
wiki_response=$(curl -s -X GET "$BASE_URL/open-apis/wiki/v2/spaces/$APP_TOKEN" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

echo "知识库响应: $wiki_response"

if echo "$wiki_response" | grep -q '"code":0'; then
    echo "✅ 知识库访问成功"
    echo "💡 这确实是知识库中的多维表格"
else
    echo "❌ 知识库访问失败"
fi

echo ""
echo "📝 诊断总结:"
echo "1. 如果多维表格应用信息获取成功，说明权限配置正确"
echo "2. 如果表格列表获取成功但找不到目标表格，说明table_id可能错误"
echo "3. 如果所有测试都失败，说明权限配置或应用安装有问题"
echo ""
echo "🔧 可能的解决方案:"
echo "1. 检查应用是否安装到正确的飞书组织/租户"
echo "2. 确认权限已发布并生效"
echo "3. 检查多维表格的共享设置"
echo "4. 联系飞书管理员确认应用状态"
