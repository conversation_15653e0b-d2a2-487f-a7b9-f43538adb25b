#!/bin/bash

# 快速测试脚本

echo "=== 紫鸟AI穿衣API 快速测试 ==="

# 检查必要文件
echo "1. 检查必要文件..."
if [ ! -f "sdk-java-5.0.6.jar" ]; then
    echo "❌ 找不到 sdk-java-5.0.6.jar"
    exit 1
fi
echo "✅ SDK文件存在"

if [ ! -d "target/classes" ]; then
    echo "❌ 找不到编译输出目录 target/classes"
    echo "请先运行编译: ./test-compile.sh"
    exit 1
fi
echo "✅ 编译输出目录存在"

# 检查配置
echo ""
echo "2. 检查配置..."
if grep -q "your_app_id_here" src/main/resources/application.yml; then
    echo "⚠️  警告: 检测到默认的app-id配置，请确保已配置正确的API密钥"
else
    echo "✅ API配置已更新"
fi

# 启动应用（后台运行）
echo ""
echo "3. 启动应用..."
echo "启动命令: java -cp \"target/classes:sdk-java-5.0.6.jar\" com.ziniao.ZiniaoAiDemoApplication"

# 启动应用并等待几秒
java -cp "target/classes:sdk-java-5.0.6.jar" com.ziniao.ZiniaoAiDemoApplication &
APP_PID=$!

echo "应用PID: $APP_PID"
echo "等待应用启动..."
sleep 10

# 检查应用是否还在运行
if kill -0 $APP_PID 2>/dev/null; then
    echo "✅ 应用启动成功！"
    
    # 测试健康检查接口
    echo ""
    echo "4. 测试API接口..."
    
    echo "测试令牌服务健康检查..."
    curl -s http://localhost:8080/api/token/health || echo "❌ 令牌服务健康检查失败"
    
    echo ""
    echo "测试穿衣服务健康检查..."
    curl -s http://localhost:8080/api/clothing/health || echo "❌ 穿衣服务健康检查失败"
    
    echo ""
    echo "测试获取令牌接口..."
    curl -s http://localhost:8080/api/token/app | head -c 200 || echo "❌ 获取令牌接口测试失败"
    
    echo ""
    echo ""
    echo "=== 测试完成 ==="
    echo "应用正在运行，访问地址: http://localhost:8080"
    echo "停止应用: kill $APP_PID"
    
else
    echo "❌ 应用启动失败"
    echo "请检查日志文件: logs/ziniao-ai-demo.log"
fi
