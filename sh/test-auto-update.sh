#!/bin/bash

# 测试自动写回功能的脚本

echo "=== 飞书多维表格自动写回功能测试 ==="
echo ""

# 服务器配置
SERVER_URL="http://localhost:8080"
API_ENDPOINT="/api/feishu/bitable/images/upload-to-local"

# 测试参数
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo "🎯 测试目标:"
echo "验证图片下载成功后，系统自动将本地URL写回多维表格"
echo ""

# 检查服务器状态
echo "1️⃣ 检查服务器状态..."
health_response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "$SERVER_URL/api/clothing/health" 2>/dev/null)
http_code=$(echo "$health_response" | grep "HTTP_CODE:" | cut -d: -f2)

if [ "$http_code" = "200" ]; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行 (HTTP $http_code)"
    echo "请先启动服务器: mvn spring-boot:run"
    exit 1
fi
echo ""

# 测试自动写回功能
echo "2️⃣ 测试自动写回功能..."

request_body=$(cat <<EOF
{
    "appToken": "$APP_TOKEN",
    "tableId": "$TABLE_ID",
    "viewId": "$VIEW_ID",
    "downloadToLocal": true,
    "updateBitableWithLocalUrl": true,
    "pageSize": 1,
    "downloadTimeout": 60,
    "maxConcurrentDownloads": 1
}
EOF
)

echo "请求参数:"
echo "$request_body" | jq . 2>/dev/null || echo "$request_body"
echo ""

echo "🚀 发送请求..."
echo "预期行为:"
echo "1. 下载图片到本地服务器"
echo "2. 获取本地访问URL"
echo "3. 自动调用飞书API更新多维表格记录"
echo "4. 在附件字段中添加 local_url, local_download_time, local_status"
echo ""

response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST "$SERVER_URL$API_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$request_body" \
    --max-time 120 2>/dev/null)

http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_CODE:/d')

echo "📊 响应结果:"
echo "状态码: $http_code"
echo "响应内容:"
echo "$response_body" | jq . 2>/dev/null || echo "$response_body"
echo ""

# 分析结果
if [ "$http_code" = "200" ]; then
    echo "✅ 接口调用成功！"
    
    if command -v jq &> /dev/null; then
        total_images=$(echo "$response_body" | jq -r '.data.totalImages // "N/A"')
        successful_downloads=$(echo "$response_body" | jq -r '.data.successfulDownloads // "N/A"')
        failed_downloads=$(echo "$response_body" | jq -r '.data.failedDownloads // "N/A"')
        
        echo ""
        echo "📈 处理统计:"
        echo "  总图片数: $total_images"
        echo "  成功下载: $successful_downloads"
        echo "  失败数量: $failed_downloads"
        
        if [ "$successful_downloads" != "0" ] && [ "$successful_downloads" != "N/A" ]; then
            echo ""
            echo "🎉 自动写回功能测试成功！"
            echo ""
            echo "✅ 已完成的操作:"
            echo "  1. ✅ 图片已下载到本地服务器"
            echo "  2. ✅ 生成了本地访问URL"
            echo "  3. ✅ 自动更新了多维表格记录"
            echo "  4. ✅ 在附件字段中添加了本地URL信息"
            echo ""
            
            # 检查本地文件
            if [ -d "uploads" ]; then
                echo "📁 本地文件检查:"
                latest_files=$(find uploads -name "*.jpg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp" | head -3)
                if [ -n "$latest_files" ]; then
                    echo "$latest_files" | while read file; do
                        echo "  ✓ $file"
                    done
                else
                    echo "  ⚠️ 未找到图片文件"
                fi
                echo ""
            fi
            
            echo "📋 多维表格更新内容:"
            echo "原有字段保持不变，新增以下字段："
            echo "  - local_url: http://localhost:8080/uploads/..."
            echo "  - local_download_time: $(date '+%Y-%m-%d %H:%M:%S')"
            echo "  - local_status: downloaded"
            echo ""
            
            echo "🔍 验证方法:"
            echo "1. 打开飞书多维表格: https://lbit922efv.feishu.cn/wiki/MgDxby4r7avigssLQnVcIQzJnm1"
            echo "2. 查看附件字段，应该包含上述本地URL信息"
            echo "3. 点击 local_url 链接，应该能访问本地图片"
            
        elif [ "$total_images" = "0" ] || [ "$total_images" = "N/A" ]; then
            echo ""
            echo "ℹ️ 没有找到图片"
            echo "可能原因："
            echo "- 指定的视图中没有图片字段"
            echo "- 图片字段为空"
            echo "- 筛选条件过于严格"
            
        else
            echo ""
            echo "⚠️ 图片下载失败"
            echo "自动写回功能无法测试，因为没有成功下载的图片"
            echo ""
            echo "🔍 可能的问题:"
            echo "- 图片URL无效或无法访问"
            echo "- 网络连接问题"
            echo "- 飞书API权限问题"
        fi
        
    else
        echo "✅ 接口调用成功，但无法解析详细结果"
        echo "请安装 jq 工具以获得更详细的分析"
    fi
    
else
    echo "❌ 接口调用失败 (HTTP $http_code)"
    echo ""
    echo "🔍 可能的问题:"
    case $http_code in
        400)
            echo "- 请求参数错误"
            echo "- 检查 appToken, tableId, viewId 是否正确"
            ;;
        401)
            echo "- 认证失败"
            echo "- 检查飞书应用配置和权限"
            ;;
        500)
            echo "- 服务器内部错误"
            echo "- 检查服务器日志获取详细错误信息"
            ;;
        *)
            echo "- 未知错误"
            echo "- 检查网络连接和服务器状态"
            ;;
    esac
fi

echo ""

# 检查日志
echo "3️⃣ 检查关键日志..."
echo ""
echo "🔍 查找自动写回相关日志:"
echo "请检查服务器日志中是否包含以下信息："
echo ""
echo "✅ 成功的日志应该显示:"
echo "  - '开始更新多维表格记录，写入本地URL信息: recordId=...'"
echo "  - '成功更新多维表格记录，写入本地URL信息: recordId=...'"
echo "  - '图片下载成功: ... -> http://localhost:8080/uploads/...'"
echo ""
echo "❌ 如果失败，日志可能显示:"
echo "  - '更新多维表格记录失败: recordId=..., error=...'"
echo "  - '更新多维表格记录异常: recordId=...'"
echo "  - '图片下载失败: ...'"
echo ""

echo "📝 查看日志命令:"
echo "tail -f logs/ziniao-api.log | grep -E '(更新多维表格记录|写入本地URL|图片下载成功)'"
echo ""

# 总结
echo "🎯 测试总结:"
echo "================================"

if [ "$http_code" = "200" ]; then
    if command -v jq &> /dev/null; then
        successful_downloads=$(echo "$response_body" | jq -r '.data.successfulDownloads // "0"')
        if [ "$successful_downloads" != "0" ]; then
            echo "🎉 自动写回功能完全正常！"
            echo ""
            echo "✅ 功能验证通过:"
            echo "  - 图片自动下载 ✅"
            echo "  - 本地URL生成 ✅"  
            echo "  - 多维表格自动更新 ✅"
            echo "  - 本地信息自动写回 ✅"
            echo ""
            echo "🎯 你现在可以:"
            echo "  1. 调用一次API，系统自动完成所有操作"
            echo "  2. 无需手动更新，本地URL自动写回表格"
            echo "  3. 在飞书多维表格中直接访问本地图片"
        else
            echo "⚠️ 功能部分正常"
            echo "接口调用成功，但图片下载失败"
            echo "需要解决图片下载问题后才能验证自动写回功能"
        fi
    else
        echo "✅ 接口调用成功"
        echo "需要检查日志确认自动写回是否正常"
    fi
else
    echo "❌ 功能测试失败"
    echo "需要先解决接口调用问题"
fi

echo ""
echo "📞 如果需要帮助:"
echo "1. 检查服务器日志: tail -f logs/ziniao-api.log"
echo "2. 验证飞书权限配置"
echo "3. 确认网络连接正常"
echo ""
echo "测试完成！"
