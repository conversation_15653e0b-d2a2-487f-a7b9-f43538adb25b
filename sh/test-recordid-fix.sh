#!/bin/bash

# 测试 recordId 修复效果的脚本

echo "=== 测试 recordId 修复效果 ==="
echo ""

# 服务器配置
SERVER_URL="http://localhost:8080"
API_ENDPOINT="/api/feishu/bitable/images/upload-to-local"

# 测试参数
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"
RECORD_ID="recqwIwhc6"

echo "🔧 测试配置:"
echo "服务器地址: $SERVER_URL"
echo "App Token: $APP_TOKEN"
echo "Table ID: $TABLE_ID"
echo "View ID: $VIEW_ID"
echo "Record ID: $RECORD_ID"
echo ""

# 检查服务器状态
echo "1️⃣ 检查服务器状态..."
health_response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "$SERVER_URL/api/clothing/health" 2>/dev/null)
http_code=$(echo "$health_response" | grep "HTTP_CODE:" | cut -d: -f2)

if [ "$http_code" = "200" ]; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行或无法访问 (HTTP $http_code)"
    echo "请先启动服务器"
    exit 1
fi
echo ""

# 测试1: 不指定 recordId 的请求（应该成功）
echo "2️⃣ 测试不指定 recordId 的请求..."

request_body_no_record=$(cat <<EOF
{
    "appToken": "$APP_TOKEN",
    "tableId": "$TABLE_ID",
    "viewId": "$VIEW_ID",
    "downloadToLocal": true,
    "pageSize": 3,
    "includeImageDetails": true,
    "downloadTimeout": 30,
    "maxConcurrentDownloads": 2
}
EOF
)

echo "请求体（无recordId）:"
echo "$request_body_no_record" | jq . 2>/dev/null || echo "$request_body_no_record"
echo ""

echo "发送请求..."
response_no_record=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST "$SERVER_URL$API_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$request_body_no_record" \
    --max-time 30 2>/dev/null)

http_code_no_record=$(echo "$response_no_record" | grep "HTTP_CODE:" | cut -d: -f2)
response_body_no_record=$(echo "$response_no_record" | sed '/HTTP_CODE:/d')

echo "响应状态码: $http_code_no_record"
echo "响应内容:"
echo "$response_body_no_record" | jq . 2>/dev/null || echo "$response_body_no_record"
echo ""

if [ "$http_code_no_record" = "200" ]; then
    echo "✅ 无recordId请求测试成功！"
else
    echo "❌ 无recordId请求测试失败 (HTTP $http_code_no_record)"
fi
echo ""

# 测试2: 指定 recordId 的请求（修复后应该成功）
echo "3️⃣ 测试指定 recordId 的请求（修复后）..."

request_body_with_record=$(cat <<EOF
{
    "appToken": "$APP_TOKEN",
    "tableId": "$TABLE_ID",
    "viewId": "$VIEW_ID",
    "recordId": "$RECORD_ID",
    "downloadToLocal": true,
    "pageSize": 3,
    "includeImageDetails": true,
    "downloadTimeout": 30,
    "maxConcurrentDownloads": 2
}
EOF
)

echo "请求体（有recordId）:"
echo "$request_body_with_record" | jq . 2>/dev/null || echo "$request_body_with_record"
echo ""

echo "发送请求..."
response_with_record=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST "$SERVER_URL$API_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$request_body_with_record" \
    --max-time 30 2>/dev/null)

http_code_with_record=$(echo "$response_with_record" | grep "HTTP_CODE:" | cut -d: -f2)
response_body_with_record=$(echo "$response_with_record" | sed '/HTTP_CODE:/d')

echo "响应状态码: $http_code_with_record"
echo "响应内容:"
echo "$response_body_with_record" | jq . 2>/dev/null || echo "$response_body_with_record"
echo ""

if [ "$http_code_with_record" = "200" ]; then
    echo "✅ 有recordId请求测试成功！修复生效！"
elif [ "$http_code_with_record" = "400" ]; then
    echo "⚠️ 有recordId请求返回400，检查错误信息..."
    if echo "$response_body_with_record" | grep -q "InvalidPageToken"; then
        echo "❌ 仍然出现InvalidPageToken错误，修复未完全生效"
    else
        echo "ℹ️ 400错误，但不是InvalidPageToken，可能是其他参数问题"
    fi
else
    echo "❌ 有recordId请求测试失败 (HTTP $http_code_with_record)"
fi
echo ""

# 测试3: 测试错误检测逻辑
echo "4️⃣ 测试错误检测逻辑..."

# 使用无效的appToken来触发认证错误
request_body_invalid_token=$(cat <<EOF
{
    "appToken": "invalid_token_test",
    "tableId": "$TABLE_ID",
    "viewId": "$VIEW_ID",
    "downloadToLocal": true,
    "pageSize": 3,
    "includeImageDetails": true,
    "downloadTimeout": 30,
    "maxConcurrentDownloads": 2
}
EOF
)

echo "使用无效token测试错误检测..."
response_invalid_token=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST "$SERVER_URL$API_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$request_body_invalid_token" \
    --max-time 30 2>/dev/null)

http_code_invalid_token=$(echo "$response_invalid_token" | grep "HTTP_CODE:" | cut -d: -f2)
response_body_invalid_token=$(echo "$response_invalid_token" | sed '/HTTP_CODE:/d')

echo "无效token响应状态码: $http_code_invalid_token"
echo "无效token响应内容:"
echo "$response_body_invalid_token" | jq . 2>/dev/null || echo "$response_body_invalid_token"
echo ""

# 总结
echo "🎯 测试总结:"
echo "================================"

if [ "$http_code_no_record" = "200" ]; then
    echo "✅ 无recordId请求: 通过"
else
    echo "❌ 无recordId请求: 失败 (HTTP $http_code_no_record)"
fi

if [ "$http_code_with_record" = "200" ]; then
    echo "✅ 有recordId请求: 通过 - 修复成功！"
elif [ "$http_code_with_record" = "400" ]; then
    if echo "$response_body_with_record" | grep -q "InvalidPageToken"; then
        echo "❌ 有recordId请求: 仍有InvalidPageToken错误"
    else
        echo "⚠️ 有recordId请求: 400错误但非InvalidPageToken"
    fi
else
    echo "❌ 有recordId请求: 失败 (HTTP $http_code_with_record)"
fi

echo ""
echo "📝 修复说明:"
echo "1. 修复了 InvalidPageToken 错误检测逻辑"
echo "2. 修复了 recordId 参数处理，不再错误地设置为 pageToken"
echo "3. recordId 现在通过 filter 参数正确处理"
echo ""
echo "🔗 相关日志:"
echo "查看服务器日志: tail -f logs/ziniao-api.log"
echo ""
echo "测试完成！"
