#!/bin/bash

# 修复后测试脚本

echo "🧪 修复后功能测试"
echo "=================="

SERVER_URL="http://39.108.93.224:18088"

echo ""
echo "1. 测试特定图片字段接口"
echo "----------------------"
echo "🧪 调用接口..."

RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "recordId": "recuRlQ3eEFJEB",
    "updateBitableWithLocalUrl": true
  }' \
  "$SERVER_URL/api/feishu/bitable/specific-image-fields")

echo "📋 响应状态:"
echo "$RESPONSE" | jq '.success, .message' 2>/dev/null

echo ""
echo "📋 检查返回的URL格式:"
echo "$RESPONSE" | jq -r '.data.records[0].imageFields | to_entries | .[].value[0].localAccessUrl' 2>/dev/null | while read -r url; do
    if [[ "$url" == *"/api/image-proxy/id/"* ]]; then
        echo "✅ 代理URL格式正确: $url"
        
        # 测试访问
        HTTP_CODE=$(curl -s -w "%{http_code}" -o /dev/null "$url")
        if [ "$HTTP_CODE" = "200" ]; then
            echo "   ✅ URL可正常访问"
        else
            echo "   ❌ URL访问失败，状态码: $HTTP_CODE"
        fi
    else
        echo "❌ 仍然是直接URL: $url"
    fi
done

echo ""
echo "2. 检查日志"
echo "-----------"
echo "📋 最新的处理日志:"
tail -n 20 /www/wwwroot/fs.vwo50.life_998/logs/ziniao-ai-demo.log | grep -E "(代理URL生成成功|图片文件不存在|StringIndexOutOfBoundsException)"

echo ""
echo "✅ 测试完成"
