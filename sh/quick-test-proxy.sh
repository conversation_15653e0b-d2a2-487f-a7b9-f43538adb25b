#!/bin/bash

# 快速测试代理URL功能

echo "🚀 快速测试代理URL功能"
echo "====================="

SERVER_URL="http://39.108.93.224:18088"

echo ""
echo "1. 测试代理URL生成接口"
echo "---------------------"
echo "🧪 调用代理URL生成接口:"
curl -X POST \
  -w "\nHTTP状态码: %{http_code}\n" \
  -d "filePath=/uploads/2025/07/26/test.png" \
  "$SERVER_URL/api/image-proxy/generate-url"

echo ""
echo ""
echo "2. 测试特定图片字段接口"
echo "----------------------"
echo "🧪 调用特定图片字段处理接口:"
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "recordId": "recuRlQ3eEFJEB",
    "updateBitableWithLocalUrl": true
  }' \
  "$SERVER_URL/api/feishu/bitable/specific-image-fields" | jq '.data.records[0].imageFields | to_entries | .[0].value[0].localAccessUrl'

echo ""
echo "3. 检查日志"
echo "-----------"
echo "📋 查看最新的URL生成日志:"
tail -n 50 /www/wwwroot/fs.vwo50.life_998/logs/ziniao-ai-demo.log | grep -E "(生成图片URL|imageProxyEnabled|代理URL)" | tail -10

echo ""
echo "✅ 测试完成"
echo ""
echo "🎯 期望结果:"
echo "   - 代理URL生成接口返回: /api/image-proxy/id/xxxxx 格式"
echo "   - 特定字段接口返回: 代理URL格式的 localAccessUrl"
echo "   - 日志显示: imageProxyEnabled: true"
