#!/bin/bash

echo "=== 重新构建包含文件上传功能的JAR文件 ==="

# 清理临时目录
rm -rf temp-jar-rebuild
mkdir -p temp-jar-rebuild

# 解压原始JAR文件
echo "解压原始JAR文件..."
cd temp-jar-rebuild
jar -xf ../target/ziniao-ai-demo-1.0.0.jar

# 复制新的类文件
echo "复制新的文件上传功能类..."
cp ../target/classes/com/ziniao/controller/FileUploadController.class BOOT-INF/classes/com/ziniao/controller/
cp ../target/classes/com/ziniao/service/FileUploadService.class BOOT-INF/classes/com/ziniao/service/
cp ../target/classes/com/ziniao/model/FileUploadResponse*.class BOOT-INF/classes/com/ziniao/model/
cp ../target/classes/com/ziniao/config/WebMvcConfig.class BOOT-INF/classes/com/ziniao/config/

# 复制更新的配置文件
echo "复制更新的配置文件..."
cp ../target/classes/application.yml BOOT-INF/classes/

# 重新打包JAR文件（使用正确的方法）
echo "重新打包JAR文件..."
jar -cfm0 ../target/ziniao-ai-demo-with-upload.jar META-INF/MANIFEST.MF .

cd ..

# 清理临时目录
rm -rf temp-jar-rebuild

echo "=== 构建完成 ==="
echo "新的JAR文件: target/ziniao-ai-demo-with-upload.jar"
echo ""
echo "启动命令:"
echo "java -jar target/ziniao-ai-demo-with-upload.jar"
