#!/bin/bash

# 精确诊断多维表格访问问题

echo "=== 多维表格访问问题精确诊断 ==="
echo ""

APP_ID="cli_a8fe3e73bd78d00d"
APP_SECRET="whPto88DEdJ9n3uBiDoDNhlTEb3KAJLU"
BASE_URL="https://open.feishu.cn"
APP_TOKEN="Wc2WwTiksil7vVkE1hqcmCmmneb"
TABLE_ID="tbl4sH8PYHUk36K0"

echo "📋 你的多维表格信息:"
echo "  链接: https://lbit922efv.feishu.cn/wiki/Wc2WwTiksil7vVkE1hqcmCmmneb?table=tbl4sH8PYHUk36K0&view=vewgI30A6c"
echo "  App Token: $APP_TOKEN"
echo "  Table ID: $TABLE_ID"
echo "  类型: 知识库中的多维表格"
echo ""

# 获取令牌
echo "🔑 获取访问令牌..."
token_response=$(curl -s -X POST "$BASE_URL/open-apis/auth/v3/app_access_token/internal" \
  -H "Content-Type: application/json" \
  -d "{\"app_id\": \"$APP_ID\", \"app_secret\": \"$APP_SECRET\"}")

if echo "$token_response" | grep -q '"code":0'; then
    app_access_token=$(echo "$token_response" | sed -n 's/.*"app_access_token":"\([^"]*\)".*/\1/p')
    echo "✅ 令牌获取成功: ${app_access_token:0:30}..."
else
    echo "❌ 令牌获取失败: $token_response"
    exit 1
fi

echo ""
echo "🔍 诊断1: 检查应用权限范围"
# 获取应用信息，检查权限
app_info_response=$(curl -s -X GET "$BASE_URL/open-apis/application/v6/applications/$APP_ID?lang=zh_cn" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

echo "应用信息响应: $app_info_response"

if echo "$app_info_response" | grep -q '"code":0'; then
    echo "✅ 应用信息获取成功"
    
    # 检查应用权限
    if echo "$app_info_response" | grep -q "bitable"; then
        echo "✅ 应用包含多维表格相关权限"
    else
        echo "⚠️ 应用可能缺少多维表格权限"
    fi
else
    echo "⚠️ 应用信息获取失败，可能是权限问题"
fi

echo ""
echo "🔍 诊断2: 尝试不同的多维表格API端点"

# 尝试1: 标准多维表格API
echo "尝试1: 标准多维表格应用信息API"
bitable_app_response=$(curl -s -X GET "$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

echo "标准API响应: $bitable_app_response"

# 尝试2: 直接访问表格记录
echo ""
echo "尝试2: 直接访问表格记录"
records_response=$(curl -s -X GET "$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN/tables/$TABLE_ID/records?page_size=1" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

echo "记录API响应: $records_response"

# 尝试3: 获取表格列表
echo ""
echo "尝试3: 获取表格列表"
tables_response=$(curl -s -X GET "$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN/tables" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

echo "表格列表响应: $tables_response"

echo ""
echo "🔍 诊断3: 分析错误模式"

# 分析所有响应中的错误
all_responses="$bitable_app_response $records_response $tables_response"

if echo "$all_responses" | grep -q "91402"; then
    echo "❌ 错误91402 (NOTEXIST) - 多维表格不存在或无权限"
    echo ""
    echo "🔧 可能的解决方案:"
    echo "1. 检查应用是否安装到正确的飞书组织/租户"
    echo "2. 确认应用有以下权限:"
    echo "   - bitable:app (获取多维表格信息)"
    echo "   - bitable:app:readonly (读取多维表格数据)"
    echo "3. 检查app_token是否正确"
    echo "4. 确认多维表格的共享设置允许应用访问"
    
elif echo "$all_responses" | grep -q "99991401"; then
    echo "❌ 错误99991401 - IP访问被拒绝"
    echo ""
    echo "🔧 解决方案:"
    echo "1. 在飞书开放平台配置IP白名单"
    echo "2. 添加当前IP到应用的安全设置中"
    
elif echo "$all_responses" | grep -q "99991664"; then
    echo "❌ 错误99991664 - 应用权限不足"
    echo ""
    echo "🔧 解决方案:"
    echo "1. 在飞书开放平台添加多维表格权限"
    echo "2. 重新发布应用权限"
    
else
    echo "🤔 未识别的错误模式，需要进一步分析"
fi

echo ""
echo "🔍 诊断4: 检查知识库权限（备选方案）"
# 由于这是知识库中的多维表格，也尝试知识库API
wiki_response=$(curl -s -X GET "$BASE_URL/open-apis/wiki/v2/spaces/$APP_TOKEN" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

echo "知识库API响应: $wiki_response"

if echo "$wiki_response" | grep -q '"code":0'; then
    echo "✅ 知识库API访问成功"
    echo "💡 这确实是知识库中的多维表格，可能需要知识库权限"
elif echo "$wiki_response" | grep -q "99991672"; then
    echo "⚠️ 缺少知识库权限，但这可能不是主要问题"
fi

echo ""
echo "📝 诊断总结:"
echo "1. 你的链接确实指向一个多维表格（知识库中的）"
echo "2. 主要问题是91402错误，表示无权限访问"
echo "3. 需要检查应用权限配置和安装状态"
echo ""
echo "🎯 推荐解决步骤:"
echo "1. 登录飞书开放平台: https://open.feishu.cn/app/$APP_ID"
echo "2. 检查并添加权限: bitable:app, bitable:app:readonly"
echo "3. 确认应用已安装到包含该多维表格的组织"
echo "4. 检查IP白名单设置"
echo "5. 如果是企业内部应用，确认在正确的企业租户下"
