#!/bin/bash

# 紫鸟AI穿衣API 启动脚本

echo "=== 紫鸟AI穿衣API 启动脚本 ==="
echo ""
echo "请选择启动方式:"
echo "1. 完整版本（包含Swagger文档）"
echo "2. 简化版本（不包含Swagger，更稳定）"
echo "3. 使用Maven启动"
echo "4. 退出"
echo ""

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "启动完整版本..."
        java -jar target/ziniao-ai-demo-1.0.0.jar
        ;;
    2)
        echo "启动简化版本..."
        java -cp "target/classes:sdk-java-5.0.6.jar:lib/*" com.ziniao.ZiniaoAiDemoSimpleApplication
        ;;
    3)
        echo "使用Maven启动..."
        if command -v mvn &> /dev/null; then
            mvn spring-boot:run
        else
            echo "错误: Maven未安装"
            exit 1
        fi
        ;;
    4)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac
