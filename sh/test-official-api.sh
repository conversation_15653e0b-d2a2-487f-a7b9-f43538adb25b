#!/bin/bash

# 测试官方文档格式的API

echo "=== 测试官方文档格式的AI穿衣API ==="

BASE_URL="http://localhost:8080"

echo ""
echo "🚀 步骤1: 测试官方格式的AI穿衣接口"

# 官方文档格式的请求参数
OFFICIAL_REQUEST='{
  "upperOriginUrl": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
  "downOriginUrl": "https://cdn.linkfox.com/ai-site/test/workbench/down-true3.jpg",
  "modelImageUrl": "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png",
  "upperImageUrl": "",
  "downImageUrl": "",
  "modelMaskImageUrl": "",
  "outputNum": 1
}'

echo "官方格式请求参数: $OFFICIAL_REQUEST"
echo ""

OFFICIAL_RESPONSE=$(curl -s -X POST "$BASE_URL/api/clothing/fittingRoom" \
  -H "Content-Type: application/json" \
  -d "$OFFICIAL_REQUEST")

echo "官方格式响应: $OFFICIAL_RESPONSE"

# 检查官方格式响应
if echo "$OFFICIAL_RESPONSE" | grep -q '"code":"0"'; then
    echo "✅ 官方格式接口调用成功！"
    
    # 提取任务ID
    TASK_ID=$(echo "$OFFICIAL_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    
    if [ ! -z "$TASK_ID" ]; then
        echo "📋 任务ID: $TASK_ID"
        echo ""
        
        echo "🔍 步骤2: 使用正确的任务ID查询结果"
        
        sleep 2  # 等待2秒
        
        RESULT_RESPONSE=$(curl -s "$BASE_URL/api/clothing/result/$TASK_ID")
        echo "结果查询响应: $RESULT_RESPONSE"
        
        if echo "$RESULT_RESPONSE" | grep -q '"code":200'; then
            echo "✅ 任务结果查询成功！"
        else
            echo "❌ 任务结果查询失败"
        fi
        
    else
        echo "❌ 无法提取任务ID"
    fi
    
else
    echo "❌ 官方格式接口调用失败"
fi

echo ""
echo "🔄 步骤3: 测试兼容格式的接口"

COMPAT_REQUEST='{
  "personImage": "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png",
  "clothingImage": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
  "clothingType": "上衣"
}'

echo "兼容格式请求参数: $COMPAT_REQUEST"
echo ""

COMPAT_RESPONSE=$(curl -s -X POST "$BASE_URL/api/clothing/process" \
  -H "Content-Type: application/json" \
  -d "$COMPAT_REQUEST")

echo "兼容格式响应: $COMPAT_RESPONSE"

if echo "$COMPAT_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 兼容格式接口调用成功！"
else
    echo "❌ 兼容格式接口调用失败"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "📚 接口说明:"
echo "1. /api/clothing/fittingRoom - 官方文档格式接口（推荐）"
echo "2. /api/clothing/process - 兼容格式接口"
echo "3. /api/clothing/result/{taskId} - 结果查询接口"
echo ""
echo "🌐 API文档: $BASE_URL/doc.html"
echo ""
echo "⚠️  注意事项:"
echo "- 任务ID应该是数字类型，不是traceId"
echo "- 图片地址有效期8小时，请及时保存"
echo "- 建议使用官方格式接口获得最佳兼容性"
