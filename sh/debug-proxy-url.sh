#!/bin/bash

# 调试图片代理URL功能脚本

echo "🔍 调试图片代理URL功能"
echo "=========================="

# 配置变量
SERVER_URL="http://*************:18088"
LOG_FILE="/www/wwwroot/fs.vwo50.life_998/logs/ziniao-ai-demo.log"

echo ""
echo "1. 检查配置文件"
echo "----------------"
echo "📄 检查 application-prod.yml 配置:"
if [ -f "application-prod.yml" ]; then
    echo "✅ 配置文件存在"
    echo ""
    echo "🔧 图片代理配置:"
    grep -A 10 "image:" application-prod.yml || echo "❌ 未找到图片代理配置"
    echo ""
    echo "🔧 服务器URL配置:"
    grep "server-base-url" application-prod.yml || echo "❌ 未找到服务器URL配置"
else
    echo "❌ 配置文件不存在"
fi

echo ""
echo "2. 检查应用状态"
echo "----------------"
echo "🔍 检查Java进程:"
ps aux | grep java | grep -v grep || echo "❌ 未找到Java进程"

echo ""
echo "🔍 检查端口监听:"
netstat -tlnp | grep :18088 || echo "❌ 端口18088未监听"

echo ""
echo "3. 检查日志中的配置信息"
echo "------------------------"
if [ -f "$LOG_FILE" ]; then
    echo "📋 查找初始化日志:"
    tail -n 1000 "$LOG_FILE" | grep -E "(飞书多维表格服务初始化|图片代理功能启用状态|ImageProxyService注入状态|服务器基础URL)" | tail -10
    
    echo ""
    echo "📋 查找最近的图片URL生成日志:"
    tail -n 500 "$LOG_FILE" | grep -E "(生成图片URL|代理URL|generateImageUrl)" | tail -10
else
    echo "❌ 日志文件不存在: $LOG_FILE"
fi

echo ""
echo "4. 测试配置接口"
echo "----------------"
echo "🧪 测试应用健康状态:"
curl -s -w "HTTP状态码: %{http_code}\n" "$SERVER_URL/actuator/health" 2>/dev/null || echo "❌ 健康检查失败"

echo ""
echo "🧪 测试Swagger文档:"
curl -s -w "HTTP状态码: %{http_code}\n" "$SERVER_URL/swagger-ui.html" -o /dev/null 2>/dev/null || echo "❌ Swagger访问失败"

echo ""
echo "5. 测试图片代理接口"
echo "--------------------"
echo "🧪 测试代理URL生成接口:"
curl -X POST \
  -w "HTTP状态码: %{http_code}\n" \
  -d "filePath=/uploads/2025/07/26/test.png" \
  "$SERVER_URL/api/image-proxy/generate-url" 2>/dev/null || echo "❌ 代理URL生成接口失败"

echo ""
echo "6. 分析问题"
echo "------------"
echo "🔍 根据您的响应分析:"
echo "   - 返回的URL: http://localhost:8080/uploads/..."
echo "   - 配置的URL: http://*************:18088"
echo "   - 问题: 应用可能使用了默认配置而不是 application-prod.yml"

echo ""
echo "💡 可能的原因:"
echo "   1. 应用没有重启，仍在使用旧配置"
echo "   2. 配置文件路径不正确"
echo "   3. Spring Profile 没有设置为 prod"
echo "   4. ImageProxyService 没有正确注入"

echo ""
echo "7. 解决方案建议"
echo "----------------"
echo "🔧 步骤1: 检查Spring Profile"
echo "   确保启动时使用: --spring.profiles.active=prod"

echo ""
echo "🔧 步骤2: 重启应用"
echo "   sudo systemctl restart your-app-name"
echo "   或者"
echo "   kill -9 \$(ps aux | grep java | grep -v grep | awk '{print \$2}')"
echo "   nohup java -jar -Dspring.profiles.active=prod ziniao-ai-demo.jar > app.log 2>&1 &"

echo ""
echo "🔧 步骤3: 验证配置生效"
echo "   重启后检查日志中的初始化信息"

echo ""
echo "8. 创建测试用例"
echo "----------------"
cat > test-proxy-config.json << 'EOF'
{
  "appToken": "your_app_token",
  "tableId": "your_table_id",
  "recordId": "your_record_id",
  "updateBitableWithLocalUrl": true
}
EOF

echo "📝 已创建测试配置文件: test-proxy-config.json"
echo ""
echo "🧪 使用以下命令测试:"
echo "curl -X POST \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d @test-proxy-config.json \\"
echo "  '$SERVER_URL/api/feishu/bitable/specific-image-fields'"

echo ""
echo "9. 实时监控日志"
echo "----------------"
echo "📋 使用以下命令监控日志:"
echo "tail -f '$LOG_FILE' | grep -E '(生成图片URL|代理URL|ImageProxy|图片代理)'"

echo ""
echo "🎯 期望看到的日志:"
echo "   - 图片代理功能启用状态: true"
echo "   - ImageProxyService注入状态: 已注入"
echo "   - 服务器基础URL: http://*************:18088"
echo "   - 生成图片URL开始"
echo "   - 条件满足，开始生成代理URL"
echo "   - 代理URL生成成功"

echo ""
echo "✅ 调试脚本执行完成"
echo ""
echo "📞 如果问题仍然存在，请："
echo "   1. 运行此脚本并提供输出结果"
echo "   2. 检查应用启动日志"
echo "   3. 确认配置文件是否被正确加载"
echo ""
echo "🔗 相关文件:"
echo "   - 配置文件: application-prod.yml"
echo "   - 日志文件: $LOG_FILE"
echo "   - 测试配置: test-proxy-config.json"
