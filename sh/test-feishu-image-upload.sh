#!/bin/bash

# 飞书图片上传到本地服务器测试脚本

echo "=== 飞书图片上传到本地服务器测试 ==="
echo ""

# 服务器配置
SERVER_URL="http://localhost:8080"
API_ENDPOINT="/api/feishu/bitable/images/upload-to-local"

# 测试参数（请根据实际情况修改）
APP_TOKEN="Wc2WwTiksil7vVkE1hqcmCmmneb"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo "🔧 测试配置:"
echo "服务器地址: $SERVER_URL"
echo "API端点: $API_ENDPOINT"
echo "App Token: $APP_TOKEN"
echo "Table ID: $TABLE_ID"
echo "View ID: $VIEW_ID"
echo ""

# 检查服务器是否运行
echo "1️⃣ 检查服务器状态..."
health_response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "$SERVER_URL/api/clothing/health" 2>/dev/null)
http_code=$(echo "$health_response" | grep "HTTP_CODE:" | cut -d: -f2)

if [ "$http_code" = "200" ]; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行或无法访问 (HTTP $http_code)"
    echo "请先启动服务器: mvn spring-boot:run"
    exit 1
fi
echo ""

# 测试1: 基本图片上传请求
echo "2️⃣ 测试基本图片上传请求..."
echo "请求URL: $SERVER_URL$API_ENDPOINT"

request_body=$(cat <<EOF
{
    "appToken": "$APP_TOKEN",
    "tableId": "$TABLE_ID",
    "viewId": "$VIEW_ID",
    "downloadToLocal": true,
    "pageSize": 5,
    "includeImageDetails": true,
    "downloadTimeout": 60,
    "maxConcurrentDownloads": 3
}
EOF
)

echo "请求体:"
echo "$request_body" | jq . 2>/dev/null || echo "$request_body"
echo ""

echo "发送请求..."
response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST "$SERVER_URL$API_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$request_body" 2>/dev/null)

http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_CODE:/d')

echo "响应状态码: $http_code"
echo "响应内容:"
echo "$response_body" | jq . 2>/dev/null || echo "$response_body"
echo ""

# 分析响应
if [ "$http_code" = "200" ]; then
    echo "✅ 请求成功！"
    
    # 尝试解析成功和失败的图片数量
    if command -v jq &> /dev/null; then
        total_images=$(echo "$response_body" | jq -r '.data.totalImages // "N/A"')
        successful_downloads=$(echo "$response_body" | jq -r '.data.successfulDownloads // "N/A"')
        failed_downloads=$(echo "$response_body" | jq -r '.data.failedDownloads // "N/A"')
        
        echo "📊 处理结果:"
        echo "  总图片数: $total_images"
        echo "  成功上传: $successful_downloads"
        echo "  失败数量: $failed_downloads"
        
        if [ "$failed_downloads" != "0" ] && [ "$failed_downloads" != "N/A" ]; then
            echo "⚠️ 部分图片上传失败，请检查服务器日志"
        fi
    fi
    
elif [ "$http_code" = "400" ]; then
    echo "❌ 请求参数错误 (400)"
    echo "可能的原因:"
    echo "  - appToken、tableId 或 viewId 不正确"
    echo "  - 请求参数格式错误"
    
elif [ "$http_code" = "401" ]; then
    echo "❌ 认证失败 (401)"
    echo "可能的原因:"
    echo "  - 飞书应用配置不正确"
    echo "  - token获取失败"
    
elif [ "$http_code" = "500" ]; then
    echo "❌ 服务器内部错误 (500)"
    echo "请检查服务器日志获取详细错误信息"
    
else
    echo "❌ 未知错误 (HTTP $http_code)"
fi

echo ""

# 测试2: 测试指定记录的图片上传
echo "3️⃣ 测试指定记录的图片上传..."

# 如果有特定的记录ID，可以在这里设置
RECORD_ID=""  # 例如: "recqwIwhc6"

if [ -n "$RECORD_ID" ]; then
    request_body_with_record=$(cat <<EOF
{
    "appToken": "$APP_TOKEN",
    "tableId": "$TABLE_ID",
    "viewId": "$VIEW_ID",
    "recordId": "$RECORD_ID",
    "downloadToLocal": true,
    "pageSize": 5,
    "includeImageDetails": true,
    "downloadTimeout": 60,
    "maxConcurrentDownloads": 3
}
EOF
    )

    echo "请求指定记录: $RECORD_ID"
    echo "请求体:"
    echo "$request_body_with_record" | jq . 2>/dev/null || echo "$request_body_with_record"
    echo ""

    response_record=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X POST "$SERVER_URL$API_ENDPOINT" \
        -H "Content-Type: application/json" \
        -d "$request_body_with_record" 2>/dev/null)

    http_code_record=$(echo "$response_record" | grep "HTTP_CODE:" | cut -d: -f2)
    response_body_record=$(echo "$response_record" | sed '/HTTP_CODE:/d')

    echo "响应状态码: $http_code_record"
    echo "响应内容:"
    echo "$response_body_record" | jq . 2>/dev/null || echo "$response_body_record"
    
    if [ "$http_code_record" = "200" ]; then
        echo "✅ 指定记录图片上传测试成功！"
    else
        echo "❌ 指定记录图片上传测试失败"
    fi
else
    echo "⏭️ 跳过指定记录测试（未设置RECORD_ID）"
fi

echo ""

# 总结
echo "🎯 测试总结:"
echo "================================"
if [ "$http_code" = "200" ]; then
    echo "✅ 基本功能测试: 通过"
else
    echo "❌ 基本功能测试: 失败 (HTTP $http_code)"
fi

if [ -n "$RECORD_ID" ]; then
    if [ "$http_code_record" = "200" ]; then
        echo "✅ 指定记录测试: 通过"
    else
        echo "❌ 指定记录测试: 失败 (HTTP $http_code_record)"
    fi
fi

echo ""
echo "📝 使用说明:"
echo "1. 确保飞书应用配置正确"
echo "2. 检查appToken、tableId、viewId是否有效"
echo "3. 查看服务器日志了解详细处理过程"
echo "4. 成功上传的图片可在 /uploads/ 目录下找到"
echo ""
echo "🔗 相关接口:"
echo "- 健康检查: GET $SERVER_URL/api/clothing/health"
echo "- 服务信息: GET $SERVER_URL/api/feishu/bitable/info"
echo "- 图片上传: POST $SERVER_URL$API_ENDPOINT"
echo ""
echo "测试完成！"
