#!/bin/bash

# 测试特定图片字段处理功能
# 专门处理 downImageUrl、downOriginUrl、modelImageUrl、modelMaskImageUrl、upperImageUrl、upperOriginUrl 这6个字段

echo "🚀 开始测试特定图片字段处理功能"
echo "=========================================="

# 服务器地址
SERVER_URL="http://localhost:8080"
API_ENDPOINT="/api/feishu/bitable/specific-image-fields"

# 测试参数 - 请根据实际情况修改
APP_TOKEN="your_app_token_here"
TABLE_ID="your_table_id_here"
VIEW_ID="your_view_id_here"
RECORD_ID="your_record_id_here"  # 可选，如果不指定则处理所有记录

echo "📋 测试配置:"
echo "- 服务器地址: $SERVER_URL"
echo "- API端点: $API_ENDPOINT"
echo "- APP_TOKEN: $APP_TOKEN"
echo "- TABLE_ID: $TABLE_ID"
echo "- VIEW_ID: $VIEW_ID"
echo "- RECORD_ID: $RECORD_ID"
echo ""

# 测试用例1: 处理所有记录的特定图片字段
echo "🧪 测试用例1: 处理所有记录的特定图片字段"
echo "----------------------------------------------"

curl -X POST "$SERVER_URL$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "pageSize": 10,
    "downloadTimeout": 60,
    "maxConcurrentDownloads": 3,
    "updateBitableWithLocalUrl": true
  }' | jq '.'

echo ""
echo ""

# 测试用例2: 处理指定记录的特定图片字段
if [ ! -z "$RECORD_ID" ]; then
    echo "🧪 测试用例2: 处理指定记录的特定图片字段"
    echo "----------------------------------------------"

    curl -X POST "$SERVER_URL$API_ENDPOINT" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer your_token_here" \
      -d '{
        "appToken": "'$APP_TOKEN'",
        "tableId": "'$TABLE_ID'",
        "recordId": "'$RECORD_ID'",
        "viewId": "'$VIEW_ID'",
        "downloadTimeout": 60,
        "maxConcurrentDownloads": 1,
        "updateBitableWithLocalUrl": true
      }' | jq '.'

    echo ""
    echo ""
fi

# 测试用例3: 只下载不回写
echo "🧪 测试用例3: 只下载图片不回写到多维表格"
echo "----------------------------------------------"

curl -X POST "$SERVER_URL$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "pageSize": 5,
    "downloadTimeout": 30,
    "maxConcurrentDownloads": 2,
    "updateBitableWithLocalUrl": false
  }' | jq '.'

echo ""
echo ""

# 测试用例4: 使用筛选条件
echo "🧪 测试用例4: 使用筛选条件处理特定记录"
echo "----------------------------------------------"

curl -X POST "$SERVER_URL$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "appToken": "'$APP_TOKEN'",
    "tableId": "'$TABLE_ID'",
    "viewId": "'$VIEW_ID'",
    "filter": "CurrentValue.[状态] = \"待处理\"",
    "pageSize": 5,
    "downloadTimeout": 45,
    "maxConcurrentDownloads": 2,
    "updateBitableWithLocalUrl": true
  }' | jq '.'

echo ""
echo ""

echo "✅ 测试完成！"
echo ""
echo "📝 功能说明:"
echo "1. 该接口专门处理以下6个图片字段："
echo "   - downImageUrl: 下载图片URL"
echo "   - downOriginUrl: 下载原图URL"
echo "   - modelImageUrl: 模型图片URL"
echo "   - modelMaskImageUrl: 模型遮罩图片URL"
echo "   - upperImageUrl: 上层图片URL"
echo "   - upperOriginUrl: 上层原图URL"
echo ""
echo "2. 处理流程："
echo "   - 从多维表格获取记录"
echo "   - 识别上述6个字段中的图片附件"
echo "   - 下载图片到本地服务器"
echo "   - 生成本地访问URL"
echo "   - 将本地URL信息写回到原字段（如果启用）"
echo ""
echo "3. 回写的字段信息："
echo "   - local_url: 本地访问URL"
echo "   - local_download_time: 下载时间"
echo "   - local_status: 下载状态"
echo ""
echo "🔗 相关命令:"
echo "- 查看日志: tail -f logs/ziniao-api.log | grep -E '(特定图片字段|processSpecificImageFields)'"
echo "- 检查上传: find uploads -name '*.jpg' -o -name '*.png' -o -name '*.gif' | head -10"
echo "- 查看API文档: open http://localhost:8080/swagger-ui.html"
echo ""
echo "⚠️  注意事项:"
echo "1. 请确保APP_TOKEN、TABLE_ID等参数正确"
echo "2. 确保飞书应用有相应的权限"
echo "3. 确保网络连接正常，能访问飞书API"
echo "4. 如果遇到99991661错误，请检查Authorization header"
