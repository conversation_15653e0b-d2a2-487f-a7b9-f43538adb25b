#!/bin/bash

# 简单的编译测试脚本

echo "=== 紫鸟AI穿衣API 编译测试 ==="

# 检查Java是否安装
if ! command -v javac &> /dev/null; then
    echo "错误: Java编译器(javac)未安装，请先安装JDK"
    exit 1
fi

echo "Java版本:"
javac -version
echo ""

# 创建输出目录
mkdir -p target/classes
mkdir -p lib

echo "=== 准备依赖 ==="

# 检查本地SDK
if [ ! -f "sdk-java-5.0.6.jar" ]; then
    echo "错误: 找不到 sdk-java-5.0.6.jar 文件"
    exit 1
fi

echo "✓ 找到本地SDK: sdk-java-5.0.6.jar"

# 下载必要的依赖（如果不存在）
DEPS=(
    "https://repo1.maven.org/maven2/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar"
    "https://repo1.maven.org/maven2/org/springframework/boot/spring-boot-starter-web/2.7.14/spring-boot-starter-web-2.7.14.jar"
    "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar"
)

for dep in "${DEPS[@]}"; do
    filename=$(basename "$dep")
    if [ ! -f "lib/$filename" ]; then
        echo "下载 $filename..."
        if command -v curl &> /dev/null; then
            curl -L -o "lib/$filename" "$dep" 2>/dev/null || echo "下载失败: $filename"
        elif command -v wget &> /dev/null; then
            wget -O "lib/$filename" "$dep" 2>/dev/null || echo "下载失败: $filename"
        else
            echo "警告: 需要curl或wget来下载依赖，跳过 $filename"
        fi
    else
        echo "✓ $filename 已存在"
    fi
done

echo ""
echo "=== 编译Java源文件 ==="

# 构建classpath
CLASSPATH="sdk-java-5.0.6.jar:lib/*"

# 编译主要源文件
echo "编译主要源文件..."
find src/main/java -name "*.java" -print0 | xargs -0 javac -cp "$CLASSPATH" -d target/classes 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✓ 编译成功！"
    
    # 复制资源文件
    if [ -d "src/main/resources" ]; then
        echo "复制资源文件..."
        cp -r src/main/resources/* target/classes/ 2>/dev/null || true
    fi
    
    echo ""
    echo "=== 编译完成 ==="
    echo "编译输出: target/classes/"
    echo ""
    echo "注意事项:"
    echo "1. 请在 src/main/resources/application.yml 中配置您的API密钥"
    echo "2. 完整运行需要Spring Boot环境，建议使用Maven: mvn spring-boot:run"
    echo "3. 本脚本仅用于验证代码编译正确性"
    
else
    echo "✗ 编译失败"
    echo ""
    echo "可能的原因:"
    echo "1. 缺少Spring Boot相关依赖"
    echo "2. 建议使用Maven进行完整构建: mvn clean compile"
    exit 1
fi
