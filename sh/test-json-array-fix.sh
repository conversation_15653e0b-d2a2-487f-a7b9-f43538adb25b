#!/bin/bash

# 测试JSON数组格式修复效果的脚本

echo "=== 飞书图片下载JSON数组格式修复测试 ==="
echo ""

# 服务器配置
SERVER_URL="http://localhost:8080"
API_ENDPOINT="/api/feishu/bitable/images/upload-to-local"

# 测试参数
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo "🔧 测试配置:"
echo "服务器地址: $SERVER_URL"
echo "App Token: $APP_TOKEN"
echo "Table ID: $TABLE_ID"
echo "View ID: $VIEW_ID"
echo ""

echo "🐛 修复的问题:"
echo "错误: java.lang.ClassCastException: com.alibaba.fastjson.JSONArray cannot be cast to java.util.Map"
echo "原因: 飞书API返回的 tmp_download_urls 是数组格式，不是对象格式"
echo ""

echo "📋 飞书API实际响应格式:"
cat << 'EOF'
{
  "code": 0,
  "data": {
    "tmp_download_urls": [  // ← 数组格式
      {
        "file_token": "PNmBblV4qoKQK2xO608c3RP6nUe",
        "tmp_download_url": "https://internal-api-drive-stream.feishu.cn/..."
      }
    ]
  },
  "msg": "success"
}
EOF
echo ""

echo "✅ 修复方案:"
echo "1. 检测 tmp_download_urls 的类型（数组 vs 对象）"
echo "2. 数组格式：取第一个元素的 tmp_download_url 字段"
echo "3. 对象格式：取第一个值（兼容性处理）"
echo ""

# 检查服务器状态
echo "1️⃣ 检查服务器状态..."
health_response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "$SERVER_URL/api/clothing/health" 2>/dev/null)
http_code=$(echo "$health_response" | grep "HTTP_CODE:" | cut -d: -f2)

if [ "$http_code" = "200" ]; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行或无法访问 (HTTP $http_code)"
    echo ""
    echo "🚀 启动服务器:"
    echo "请先启动服务器，然后重新运行此测试脚本"
    echo ""
    echo "启动命令示例:"
    echo "1. mvn spring-boot:run"
    echo "2. java -jar target/ziniao-ai-demo-1.0.0.jar"
    echo "3. 在IDE中运行 ZiniaoAiDemoApplication"
    exit 1
fi
echo ""

# 测试修复效果
echo "2️⃣ 测试JSON数组格式修复..."

request_body=$(cat <<EOF
{
    "appToken": "$APP_TOKEN",
    "tableId": "$TABLE_ID",
    "viewId": "$VIEW_ID",
    "downloadToLocal": true,
    "pageSize": 1,
    "includeImageDetails": true,
    "downloadTimeout": 60,
    "maxConcurrentDownloads": 1
}
EOF
)

echo "请求体（单个图片测试）:"
echo "$request_body" | jq . 2>/dev/null || echo "$request_body"
echo ""

echo "发送请求..."
response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST "$SERVER_URL$API_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$request_body" \
    --max-time 90 2>/dev/null)

http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_CODE:/d')

echo "响应状态码: $http_code"
echo "响应内容:"
echo "$response_body" | jq . 2>/dev/null || echo "$response_body"
echo ""

# 分析响应
if [ "$http_code" = "200" ]; then
    echo "✅ 请求成功！"
    
    # 尝试解析成功和失败的图片数量
    if command -v jq &> /dev/null; then
        total_images=$(echo "$response_body" | jq -r '.data.totalImages // "N/A"')
        successful_downloads=$(echo "$response_body" | jq -r '.data.successfulDownloads // "N/A"')
        failed_downloads=$(echo "$response_body" | jq -r '.data.failedDownloads // "N/A"')
        
        echo "📊 处理结果:"
        echo "  总图片数: $total_images"
        echo "  成功下载: $successful_downloads"
        echo "  失败数量: $failed_downloads"
        
        if [ "$successful_downloads" != "0" ] && [ "$successful_downloads" != "N/A" ]; then
            echo ""
            echo "🎉 JSON数组格式修复成功！"
            echo "✅ 成功解析飞书API的数组响应格式"
            echo "✅ 成功提取真实下载链接"
            echo "✅ 成功下载图片到本地"
            
            # 检查上传目录
            if [ -d "uploads" ]; then
                echo ""
                echo "📁 检查上传目录:"
                find uploads -name "*.jpg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp" | head -3 | while read file; do
                    echo "  ✓ $file"
                done
            fi
            
        elif [ "$failed_downloads" = "$total_images" ] && [ "$total_images" != "0" ]; then
            echo ""
            echo "⚠️ 仍有问题需要解决"
            echo "虽然JSON解析可能已修复，但图片下载仍然失败"
            echo ""
            echo "🔍 可能的其他问题:"
            echo "1. 真实下载链接访问失败"
            echo "2. 图片文件保存权限问题"
            echo "3. 网络连接问题"
            
        else
            echo ""
            echo "ℹ️ 没有找到图片或图片数量为0"
        fi
        
    fi
    
elif [ "$http_code" = "500" ]; then
    echo "❌ 服务器内部错误 (500)"
    echo ""
    echo "🔍 检查是否仍有ClassCastException:"
    echo "如果日志中仍然出现 'JSONArray cannot be cast to Map' 错误，"
    echo "说明修复还没有生效，需要重新编译和重启服务器。"
    
else
    echo "❌ 请求失败 (HTTP $http_code)"
fi

echo ""

# 检查日志中的关键信息
echo "3️⃣ 检查关键日志信息..."

echo "🔍 查找相关日志:"
echo "请检查服务器日志中是否包含以下信息："
echo ""
echo "✅ 成功的日志应该显示:"
echo "  - 'batch_get_tmp_download_url响应状态码: 200'"
echo "  - '成功获取真实下载链接（数组格式）: https://...'"
echo "  - '图片信息 - Content-Type: image/...'"
echo "  - '图片下载并保存成功'"
echo ""
echo "❌ 如果仍然失败，日志可能显示:"
echo "  - 'java.lang.ClassCastException: JSONArray cannot be cast to Map'"
echo "  - '获取真实下载链接失败'"
echo "  - 'URL指向的不是图片文件，Content-Type: application/json'"
echo ""

# 总结
echo "🎯 修复总结:"
echo "================================"

if [ "$http_code" = "200" ]; then
    if command -v jq &> /dev/null; then
        successful_downloads=$(echo "$response_body" | jq -r '.data.successfulDownloads // "0"')
        if [ "$successful_downloads" != "0" ]; then
            echo "🎉 修复完全成功！"
            echo "✅ JSON数组格式解析正常"
            echo "✅ 图片下载功能正常"
        else
            echo "⚠️ 修复部分成功"
            echo "✅ 接口调用正常"
            echo "❓ 图片下载可能仍有问题"
        fi
    else
        echo "✅ 接口调用成功"
        echo "❓ 需要检查具体下载结果"
    fi
elif [ "$http_code" = "500" ]; then
    echo "❌ 修复可能未生效"
    echo "需要重新编译和重启服务器"
else
    echo "❌ 修复验证失败"
    echo "需要进一步排查问题"
fi

echo ""
echo "📝 下一步操作:"
echo "1. 如果修复成功：可以增加pageSize测试更多图片"
echo "2. 如果仍有问题：检查服务器日志获取详细错误信息"
echo "3. 如果是500错误：重新编译和重启服务器"
echo ""
echo "🔗 相关命令:"
echo "- 查看日志: tail -f logs/ziniao-api.log | grep -E '(getRealDownloadUrl|tmp_download_url|ClassCastException)'"
echo "- 检查上传: find uploads -name '*.jpg' -o -name '*.png' -o -name '*.gif'"
echo "- 重启服务: mvn spring-boot:run"
echo ""
echo "测试完成！"
