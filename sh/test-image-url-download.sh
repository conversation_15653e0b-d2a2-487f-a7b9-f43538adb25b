#!/bin/bash

echo "=== 测试图片URL下载功能 ==="

# 服务器地址
SERVER_URL="http://localhost:8080"

# 测试图片URL（使用一个公开的测试图片）
TEST_IMAGE_URL="https://httpbin.org/image/jpeg"

echo "1. 测试服务器是否运行..."
curl -s "$SERVER_URL/api/file/health" > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ 服务器运行正常"
else
    echo "✗ 服务器未运行，请先启动应用"
    exit 1
fi

echo ""
echo "2. 测试表单参数方式下载图片..."
echo "请求URL: $SERVER_URL/api/file/download-from-url"
echo "图片URL: $TEST_IMAGE_URL"

response=$(curl -s -X POST \
  "$SERVER_URL/api/file/download-from-url" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "imageUrl=$TEST_IMAGE_URL")

echo "响应: $response"

echo ""
echo "3. 测试JSON格式下载图片..."
echo "请求URL: $SERVER_URL/api/file/download-image"

json_response=$(curl -s -X POST \
  "$SERVER_URL/api/file/download-image" \
  -H "Content-Type: application/json" \
  -d "{\"imageUrl\":\"$TEST_IMAGE_URL\",\"customFileName\":\"test_image\"}")

echo "响应: $json_response"

echo ""
echo "=== 测试完成 ==="
