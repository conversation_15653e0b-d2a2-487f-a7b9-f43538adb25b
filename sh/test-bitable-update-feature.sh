#!/bin/bash

# 测试飞书多维表格本地URL写回功能的脚本

echo "=== 飞书多维表格本地URL写回功能测试 ==="
echo ""

# 服务器配置
SERVER_URL="http://localhost:8080"
API_ENDPOINT="/api/feishu/bitable/images/upload-to-local"
UPDATE_ENDPOINT="/api/feishu/bitable/records"

# 测试参数
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"
VIEW_ID="vewgI30A6c"

echo "🔧 测试配置:"
echo "服务器地址: $SERVER_URL"
echo "App Token: $APP_TOKEN"
echo "Table ID: $TABLE_ID"
echo "View ID: $VIEW_ID"
echo ""

echo "🎯 新功能说明:"
echo "1. 下载图片到本地服务器"
echo "2. 自动将本地URL写回多维表格对应的附件字段"
echo "3. 在原有附件信息基础上添加本地访问信息"
echo ""

# 检查服务器状态
echo "1️⃣ 检查服务器状态..."
health_response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "$SERVER_URL/api/clothing/health" 2>/dev/null)
http_code=$(echo "$health_response" | grep "HTTP_CODE:" | cut -d: -f2)

if [ "$http_code" = "200" ]; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行或无法访问 (HTTP $http_code)"
    echo ""
    echo "🚀 启动服务器:"
    echo "请先启动服务器，然后重新运行此测试脚本"
    exit 1
fi
echo ""

# 测试1: 带本地URL写回的图片下载
echo "2️⃣ 测试图片下载并写回本地URL..."

request_body_with_update=$(cat <<EOF
{
    "appToken": "$APP_TOKEN",
    "tableId": "$TABLE_ID",
    "viewId": "$VIEW_ID",
    "downloadToLocal": true,
    "updateBitableWithLocalUrl": true,
    "pageSize": 1,
    "includeImageDetails": true,
    "downloadTimeout": 60,
    "maxConcurrentDownloads": 1
}
EOF
)

echo "请求体（启用写回功能）:"
echo "$request_body_with_update" | jq . 2>/dev/null || echo "$request_body_with_update"
echo ""

echo "发送请求..."
response_with_update=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST "$SERVER_URL$API_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$request_body_with_update" \
    --max-time 120 2>/dev/null)

http_code_with_update=$(echo "$response_with_update" | grep "HTTP_CODE:" | cut -d: -f2)
response_body_with_update=$(echo "$response_with_update" | sed '/HTTP_CODE:/d')

echo "响应状态码: $http_code_with_update"
echo "响应内容:"
echo "$response_body_with_update" | jq . 2>/dev/null || echo "$response_body_with_update"
echo ""

# 分析响应
if [ "$http_code_with_update" = "200" ]; then
    echo "✅ 请求成功！"
    
    # 尝试解析成功和失败的图片数量
    if command -v jq &> /dev/null; then
        total_images=$(echo "$response_body_with_update" | jq -r '.data.totalImages // "N/A"')
        successful_downloads=$(echo "$response_body_with_update" | jq -r '.data.successfulDownloads // "N/A"')
        failed_downloads=$(echo "$response_body_with_update" | jq -r '.data.failedDownloads // "N/A"')
        
        echo "📊 处理结果:"
        echo "  总图片数: $total_images"
        echo "  成功下载: $successful_downloads"
        echo "  失败数量: $failed_downloads"
        
        if [ "$successful_downloads" != "0" ] && [ "$successful_downloads" != "N/A" ]; then
            echo ""
            echo "🎉 图片下载和URL写回功能测试成功！"
            echo "✅ 图片已下载到本地服务器"
            echo "✅ 本地URL已写回多维表格"
            echo ""
            
            # 检查上传目录
            if [ -d "uploads" ]; then
                echo "📁 检查上传目录:"
                find uploads -name "*.jpg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp" | head -3 | while read file; do
                    echo "  ✓ $file"
                done
                echo ""
            fi
            
            echo "📋 多维表格中的附件字段现在应该包含:"
            echo "  - file_token: 原始文件token"
            echo "  - name: 原始文件名"
            echo "  - url: 飞书原始URL"
            echo "  - local_url: 本地服务器URL ⭐ 新增"
            echo "  - local_download_time: 下载时间 ⭐ 新增"
            echo "  - local_status: 下载状态 ⭐ 新增"
            
        elif [ "$failed_downloads" = "$total_images" ] && [ "$total_images" != "0" ]; then
            echo ""
            echo "⚠️ 图片下载失败，无法测试写回功能"
            echo "请先解决图片下载问题"
            
        else
            echo ""
            echo "ℹ️ 没有找到图片或图片数量为0"
        fi
        
    fi
    
else
    echo "❌ 请求失败 (HTTP $http_code_with_update)"
fi

echo ""

# 测试2: 不启用写回功能的对比测试
echo "3️⃣ 对比测试（不启用写回功能）..."

request_body_no_update=$(cat <<EOF
{
    "appToken": "$APP_TOKEN",
    "tableId": "$TABLE_ID",
    "viewId": "$VIEW_ID",
    "downloadToLocal": true,
    "updateBitableWithLocalUrl": false,
    "pageSize": 1,
    "includeImageDetails": true,
    "downloadTimeout": 60,
    "maxConcurrentDownloads": 1
}
EOF
)

echo "请求体（禁用写回功能）:"
echo "$request_body_no_update" | jq . 2>/dev/null || echo "$request_body_no_update"
echo ""

echo "发送请求..."
response_no_update=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST "$SERVER_URL$API_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$request_body_no_update" \
    --max-time 120 2>/dev/null)

http_code_no_update=$(echo "$response_no_update" | grep "HTTP_CODE:" | cut -d: -f2)
response_body_no_update=$(echo "$response_no_update" | sed '/HTTP_CODE:/d')

echo "响应状态码: $http_code_no_update"
echo "响应内容:"
echo "$response_body_no_update" | jq . 2>/dev/null || echo "$response_body_no_update"
echo ""

if [ "$http_code_no_update" = "200" ]; then
    echo "✅ 对比测试成功！"
    echo "📝 说明: 图片已下载但未写回多维表格"
else
    echo "❌ 对比测试失败 (HTTP $http_code_no_update)"
fi

echo ""

# 测试3: 手动更新记录测试（如果有记录ID）
echo "4️⃣ 手动更新记录测试..."

# 这里需要一个实际的记录ID，可以从前面的响应中获取
# 为了演示，我们使用一个示例记录ID
SAMPLE_RECORD_ID="recqwIwhc6"

manual_update_body=$(cat <<EOF
{
    "appToken": "$APP_TOKEN",
    "tableId": "$TABLE_ID",
    "fields": {
        "备注": "本地URL写回功能测试 - $(date '+%Y-%m-%d %H:%M:%S')"
    }
}
EOF
)

echo "手动更新记录请求体:"
echo "$manual_update_body" | jq . 2>/dev/null || echo "$manual_update_body"
echo ""

echo "发送手动更新请求..."
manual_update_response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X PUT "$SERVER_URL$UPDATE_ENDPOINT/$SAMPLE_RECORD_ID" \
    -H "Content-Type: application/json" \
    -d "$manual_update_body" \
    --max-time 30 2>/dev/null)

manual_http_code=$(echo "$manual_update_response" | grep "HTTP_CODE:" | cut -d: -f2)
manual_response_body=$(echo "$manual_update_response" | sed '/HTTP_CODE:/d')

echo "手动更新响应状态码: $manual_http_code"
echo "手动更新响应内容:"
echo "$manual_response_body" | jq . 2>/dev/null || echo "$manual_response_body"
echo ""

if [ "$manual_http_code" = "200" ]; then
    echo "✅ 手动更新记录功能正常！"
else
    echo "⚠️ 手动更新记录失败，可能是记录ID不存在或权限问题"
fi

echo ""

# 总结
echo "🎯 测试总结:"
echo "================================"

if [ "$http_code_with_update" = "200" ]; then
    if command -v jq &> /dev/null; then
        successful_downloads=$(echo "$response_body_with_update" | jq -r '.data.successfulDownloads // "0"')
        if [ "$successful_downloads" != "0" ]; then
            echo "🎉 本地URL写回功能完全正常！"
            echo "✅ 图片下载功能正常"
            echo "✅ 多维表格更新功能正常"
            echo "✅ 本地URL自动写回功能正常"
        else
            echo "⚠️ 功能部分正常"
            echo "✅ 接口调用正常"
            echo "❓ 图片下载可能有问题"
        fi
    else
        echo "✅ 接口调用成功"
        echo "❓ 需要检查具体功能效果"
    fi
else
    echo "❌ 功能测试失败"
    echo "需要检查服务器日志获取详细错误信息"
fi

echo ""
echo "📝 功能说明:"
echo "1. updateBitableWithLocalUrl=true: 下载图片并写回本地URL到多维表格"
echo "2. updateBitableWithLocalUrl=false: 只下载图片，不更新多维表格"
echo "3. 写回的字段包括: local_url, local_download_time, local_status"
echo ""
echo "🔗 相关命令:"
echo "- 查看日志: tail -f logs/ziniao-api.log | grep -E '(updateBitableRecordWithLocalUrls|更新多维表格记录)'"
echo "- 检查上传: find uploads -name '*.jpg' -o -name '*.png' -o -name '*.gif'"
echo "- 手动更新: curl -X PUT $SERVER_URL$UPDATE_ENDPOINT/{recordId} -H 'Content-Type: application/json' -d '{...}'"
echo ""
echo "测试完成！"
