#!/bin/bash

# 测试知识库 vs 多维表格的区别

echo "=== 知识库 vs 多维表格测试 ==="
echo ""

APP_ID="cli_a8fe3e73bd78d00d"
APP_SECRET="whPto88DEdJ9n3uBiDoDNhlTEb3KAJLU"
BASE_URL="https://open.feishu.cn"
APP_TOKEN="Wc2WwTiksil7vVkE1hqcmCmmneb"

# 获取令牌
echo "🔑 获取访问令牌..."
token_response=$(curl -s -X POST "$BASE_URL/open-apis/auth/v3/app_access_token/internal" \
  -H "Content-Type: application/json" \
  -d "{\"app_id\": \"$APP_ID\", \"app_secret\": \"$APP_SECRET\"}")

if echo "$token_response" | grep -q '"code":0'; then
    app_access_token=$(echo "$token_response" | sed -n 's/.*"app_access_token":"\([^"]*\)".*/\1/p')
    echo "✅ 令牌获取成功"
else
    echo "❌ 令牌获取失败"
    exit 1
fi

echo ""
echo "🧪 测试1: 作为多维表格访问"
bitable_response=$(curl -s -X GET "$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

echo "多维表格响应: $bitable_response"

if echo "$bitable_response" | grep -q '"code":0'; then
    echo "✅ 这是一个多维表格"
else
    echo "❌ 这不是一个多维表格或无权限访问"
fi

echo ""
echo "🧪 测试2: 作为知识库访问"
wiki_response=$(curl -s -X GET "$BASE_URL/open-apis/wiki/v2/spaces/$APP_TOKEN" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

echo "知识库响应: $wiki_response"

if echo "$wiki_response" | grep -q '"code":0'; then
    echo "✅ 这是一个知识库"
else
    echo "❌ 这不是一个知识库或无权限访问"
fi

echo ""
echo "📋 分析结果:"
echo "根据你的链接 https://lbit922efv.feishu.cn/wiki/Wc2WwTiksil7vVkE1hqcmCmmneb"
echo "这看起来是一个知识库(wiki)而不是多维表格(bitable)"
echo ""
echo "🔧 解决方案:"
echo "1. 如果你需要访问多维表格，请提供正确的多维表格链接"
echo "   格式: https://xxx.feishu.cn/base/xxxxx"
echo ""
echo "2. 如果你需要访问知识库中的数据表，需要使用知识库API"
echo "   但知识库API通常不支持直接获取表格数据"
echo ""
echo "3. 建议：将数据迁移到独立的多维表格应用中"
