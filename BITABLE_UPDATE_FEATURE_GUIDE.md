# 🔄 飞书多维表格本地URL写回功能指南

## 🎯 功能概述

在原有的图片下载功能基础上，新增了**本地URL写回多维表格**的功能。现在系统可以：

1. **下载图片到本地服务器** ✅ (原有功能)
2. **获取本地访问URL** ✅ (原有功能)  
3. **将本地URL写回多维表格** 🆕 (新增功能)

## 🔧 功能实现

### 1. 新增请求参数

在 `FeishuImageDownloadRequest` 中新增了控制参数：

```java
@ApiModelProperty(value = "是否将本地URL写回多维表格", example = "true")
private Boolean updateBitableWithLocalUrl = true;
```

### 2. 自动写回逻辑

当图片成功下载到本地后，系统会自动：

1. **构建更新数据**：在原有附件信息基础上添加本地访问信息
2. **调用更新API**：使用飞书多维表格更新接口
3. **写入本地信息**：添加 `local_url`、`local_download_time`、`local_status` 字段

### 3. 写回的字段信息

原有附件字段格式：
```json
{
  "file_token": "boxcnxe8OIukXXXXXXXXXXXXXX",
  "name": "image.jpg",
  "type": "image/jpeg",
  "size": 1024000,
  "url": "https://xxx.feishu.cn/space/api/box/stream/download/asynccode/?code=xxx"
}
```

写回后的附件字段格式：
```json
{
  "file_token": "boxcnxe8OIukXXXXXXXXXXXXXX",
  "name": "image.jpg",
  "type": "image/jpeg",
  "size": 1024000,
  "url": "https://xxx.feishu.cn/space/api/box/stream/download/asynccode/?code=xxx",
  "local_url": "http://localhost:8080/uploads/2025/07/25/20250725_123456_abc123.jpg",
  "local_download_time": "2025-07-25 12:34:56",
  "local_status": "downloaded"
}
```

## 📋 API接口

### 1. 图片下载并写回URL

**接口地址**: `POST /api/feishu/bitable/images/upload-to-local`

**请求参数**:
```json
{
  "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
  "tableId": "tbl4sH8PYHUk36K0",
  "viewId": "vewgI30A6c",
  "downloadToLocal": true,
  "updateBitableWithLocalUrl": true,  // 🆕 控制是否写回
  "pageSize": 3,
  "downloadTimeout": 60,
  "maxConcurrentDownloads": 1
}
```

### 2. 手动更新记录

**接口地址**: `PUT /api/feishu/bitable/records/{recordId}`

**请求参数**:
```json
{
  "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
  "tableId": "tbl4sH8PYHUk36K0",
  "fields": {
    "字段名": "更新的值",
    "图片字段": [
      {
        "file_token": "boxcnxe8OIukXXXXXXXXXXXXXX",
        "name": "image.jpg",
        "local_url": "http://localhost:8080/uploads/image.jpg"
      }
    ]
  }
}
```

## 🚀 使用示例

### 示例1: 启用写回功能

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "downloadToLocal": true,
    "updateBitableWithLocalUrl": true,
    "pageSize": 2
  }'
```

**效果**：
- ✅ 下载图片到本地
- ✅ 获取本地访问URL
- ✅ 将本地URL写回多维表格

### 示例2: 禁用写回功能

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "downloadToLocal": true,
    "updateBitableWithLocalUrl": false,
    "pageSize": 2
  }'
```

**效果**：
- ✅ 下载图片到本地
- ✅ 获取本地访问URL
- ❌ 不更新多维表格

### 示例3: 手动更新记录

```bash
curl -X PUT "http://localhost:8080/api/feishu/bitable/records/recqwIwhc6" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fields": {
      "备注": "手动更新测试"
    }
  }'
```

## 🔍 测试验证

### 1. 运行测试脚本

```bash
# 运行完整的功能测试
./test-bitable-update-feature.sh
```

### 2. 检查日志

```bash
# 查看写回相关日志
tail -f logs/ziniao-api.log | grep -E "(updateBitableRecordWithLocalUrls|更新多维表格记录)"

# 查看图片下载日志
tail -f logs/ziniao-api.log | grep -E "(图片下载成功|本地URL)"
```

### 3. 验证多维表格

1. **打开飞书多维表格**
2. **查看附件字段**：应该包含新增的本地URL信息
3. **检查字段内容**：确认 `local_url`、`local_download_time`、`local_status` 字段存在

## ⚙️ 配置选项

### 1. 控制写回行为

```java
// 只下载，不写回
request.setUpdateBitableWithLocalUrl(false);

// 下载并写回（默认）
request.setUpdateBitableWithLocalUrl(true);
```

### 2. 写回条件

系统只在以下条件同时满足时才会写回：
- `downloadToLocal = true` (启用本地下载)
- `updateBitableWithLocalUrl = true` (启用写回功能)
- 图片下载成功

## 🛠️ 技术实现

### 1. 核心方法

```java
// 处理记录图片并写回URL
private FeishuImageDownloadResponse.RecordImageInfo processRecordImages(
    FeishuBitableResponse.Record record, 
    FeishuImageDownloadRequest request)

// 更新多维表格记录
private void updateBitableRecordWithLocalUrls(
    String recordId, 
    Map<String, Object> updatedFields, 
    FeishuImageDownloadRequest request)

// 更新记录的内部实现
private FeishuBitableUpdateResponse updateRecordInternal(
    FeishuBitableUpdateRequest request)
```

### 2. 写回流程

```
1. 下载图片到本地 ✅
2. 获取本地访问URL ✅
3. 构建更新字段数据 🆕
4. 调用飞书更新API 🆕
5. 写入本地URL信息 🆕
```

## 🎯 使用场景

### 1. 图片本地化存储

- **场景**：将飞书多维表格中的图片下载到本地服务器
- **优势**：提高访问速度，减少外部依赖
- **配置**：`downloadToLocal=true, updateBitableWithLocalUrl=true`

### 2. 数据备份

- **场景**：备份重要图片资源到本地
- **优势**：数据安全，离线访问
- **配置**：`downloadToLocal=true, updateBitableWithLocalUrl=true`

### 3. 批量处理

- **场景**：批量下载和处理多维表格中的图片
- **优势**：自动化处理，状态跟踪
- **配置**：根据需要设置 `pageSize` 和并发参数

## 📝 注意事项

1. **权限要求**：需要多维表格的读写权限
2. **网络稳定**：写回操作需要稳定的网络连接
3. **存储空间**：确保本地服务器有足够的存储空间
4. **并发控制**：建议设置合理的 `maxConcurrentDownloads` 值
5. **错误处理**：写回失败不会影响图片下载功能

## 🎉 总结

新增的本地URL写回功能实现了完整的图片本地化流程：

1. **✅ 下载图片**：从飞书下载到本地服务器
2. **✅ 生成URL**：生成本地访问链接
3. **✅ 写回表格**：将本地信息更新到多维表格
4. **✅ 状态跟踪**：记录下载时间和状态

现在你可以实现真正的图片本地化存储，并在多维表格中保留完整的访问信息！🚀
