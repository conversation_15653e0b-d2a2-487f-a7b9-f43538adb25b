# 🔄 飞书多维表格自动写回功能使用指南

## 🎯 功能说明

**自动写回功能**已经实现！你只需要调用一次API，系统就会自动完成以下所有操作：

1. **✅ 下载图片**：从飞书多维表格下载图片到本地服务器
2. **✅ 生成本地URL**：为每个图片生成本地访问链接
3. **✅ 自动写回**：将本地URL信息自动写回到飞书多维表格中

**无需手动操作**，一次API调用完成所有流程！

## 🚀 使用方法

### 基本用法（自动写回已默认启用）

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "downloadToLocal": true,
    "pageSize": 3
  }'
```

**系统自动执行**：
- ✅ 下载图片到 `uploads/` 目录
- ✅ 生成本地URL：`http://localhost:8080/uploads/2025/07/25/image.jpg`
- ✅ 自动调用飞书API更新多维表格记录
- ✅ 在附件字段中添加本地URL信息

### 明确启用自动写回

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "downloadToLocal": true,
    "updateBitableWithLocalUrl": true,  // 明确启用
    "pageSize": 3
  }'
```

### 禁用自动写回（仅下载）

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "downloadToLocal": true,
    "updateBitableWithLocalUrl": false, // 禁用写回
    "pageSize": 3
  }'
```

## 📋 写回效果

### 原始附件字段
```json
{
  "file_token": "boxcnxe8OIukXXXXXXXXXXXXXX",
  "name": "image.jpg",
  "type": "image/jpeg",
  "size": 1024000,
  "url": "https://xxx.feishu.cn/space/api/box/stream/download/asynccode/?code=xxx"
}
```

### 自动写回后的附件字段
```json
{
  "file_token": "boxcnxe8OIukXXXXXXXXXXXXXX",
  "name": "image.jpg",
  "type": "image/jpeg", 
  "size": 1024000,
  "url": "https://xxx.feishu.cn/space/api/box/stream/download/asynccode/?code=xxx",
  "local_url": "http://localhost:8080/uploads/2025/07/25/20250725_123456_abc123.jpg",
  "local_download_time": "2025-07-25 12:34:56",
  "local_status": "downloaded"
}
```

## 🔍 验证方法

### 1. 运行测试脚本
```bash
# 启动服务器
mvn spring-boot:run

# 运行自动写回测试
./test-auto-update.sh
```

### 2. 检查日志
```bash
# 查看自动写回相关日志
tail -f logs/ziniao-api.log | grep -E "(开始自动写回|成功更新多维表格记录|为图片添加本地URL)"
```

**成功的日志示例**：
```
INFO  - 检测到成功下载的图片，准备构建更新字段: fieldName=图片字段, successCount=2
INFO  - 为图片添加本地URL信息: name=image1.jpg, localUrl=http://localhost:8080/uploads/...
INFO  - 为图片添加本地URL信息: name=image2.png, localUrl=http://localhost:8080/uploads/...
INFO  - 字段更新数据已准备: fieldName=图片字段, attachmentCount=2
INFO  - 开始自动写回本地URL到多维表格: recordId=recqwIwhc6, fieldsToUpdate=[图片字段]
INFO  - 开始更新多维表格记录，写入本地URL信息: recordId=recqwIwhc6, fields=[图片字段]
INFO  - 成功更新多维表格记录，写入本地URL信息: recordId=recqwIwhc6
```

### 3. 检查多维表格
1. **打开飞书多维表格**：https://lbit922efv.feishu.cn/wiki/MgDxby4r7avigssLQnVcIQzJnm1
2. **查看附件字段**：应该包含新增的 `local_url`、`local_download_time`、`local_status` 字段
3. **点击本地链接**：`local_url` 应该能直接访问本地图片

### 4. 检查本地文件
```bash
# 查看下载的图片文件
find uploads -name "*.jpg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp"

# 查看最新下载的文件
ls -lt uploads/2025/07/25/ | head -5
```

## ⚙️ 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `downloadToLocal` | Boolean | `true` | 是否下载图片到本地 |
| `updateBitableWithLocalUrl` | Boolean | `true` | 是否自动写回本地URL |
| `pageSize` | Integer | `100` | 每次处理的记录数 |
| `downloadTimeout` | Integer | `30` | 图片下载超时时间（秒） |
| `maxConcurrentDownloads` | Integer | `5` | 最大并发下载数 |

## 🔧 工作流程

```
1. 调用API
   ↓
2. 获取多维表格记录
   ↓
3. 识别图片字段
   ↓
4. 下载图片到本地
   ↓
5. 生成本地访问URL
   ↓
6. 构建更新数据（添加local_url等字段）
   ↓
7. 自动调用飞书API更新记录
   ↓
8. 返回处理结果
```

## 🎯 使用场景

### 1. 图片本地化存储
```bash
# 一次性将多维表格中的所有图片本地化
curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "downloadToLocal": true,
    "pageSize": 50
  }'
```

### 2. 指定记录处理
```bash
# 只处理特定记录的图片
curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "recordId": "recqwIwhc6",
    "downloadToLocal": true
  }'
```

### 3. 指定图片字段
```bash
# 只处理特定的图片字段
curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "imageFields": ["产品图片", "详情图"],
    "downloadToLocal": true
  }'
```

## 📝 注意事项

1. **权限要求**：确保飞书应用有多维表格的读写权限
2. **网络稳定**：自动写回需要稳定的网络连接
3. **存储空间**：确保服务器有足够的存储空间
4. **并发控制**：合理设置 `maxConcurrentDownloads` 避免过载
5. **错误处理**：写回失败不会影响图片下载功能

## 🎉 总结

**自动写回功能已完全实现**！你现在可以：

- ✅ **一次API调用**：完成图片下载和URL写回
- ✅ **无需手动操作**：系统自动处理所有步骤
- ✅ **完整信息记录**：本地URL、下载时间、状态等
- ✅ **灵活控制**：可以启用或禁用写回功能

只需要调用一次API，系统就会自动将本地URL写回到飞书多维表格中！🚀
