# 🖼️ 飞书图片下载修复指南

## 问题分析

从你提供的最新日志可以看出，问题已经从**token认证错误**转变为**URL类型错误**：

### 错误日志分析
```
2025-07-25 00:52:45.292 [http-nio-8080-exec-1] DEBUG c.z.service.FeishuBitableService - 图片信息 - Content-Type: application/json; charset=utf-8, Content-Length: 364
2025-07-25 00:52:45.292 [http-nio-8080-exec-1] ERROR c.z.service.FeishuBitableService - URL指向的不是图片文件，Content-Type: application/json; charset=utf-8
```

### 问题根源

**URL类型错误**：我们直接访问了 `batch_get_tmp_download_url` API端点，但这个URL返回的是JSON响应，不是图片文件本身！

```
https://open.feishu.cn/open-apis/drive/v1/medias/batch_get_tmp_download_url?file_tokens=PNmBblV4qoKQK2xO608c3RP6nUe&extra=...
```

这个URL返回的是包含实际下载链接的JSON，格式如下：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "tmp_download_urls": {
      "PNmBblV4qoKQK2xO608c3RP6nUe": "https://actual-download-url.com/image.gif"
    }
  }
}
```

## 修复方案

### 1. 创建真实下载链接获取方法

我已经创建了 `getRealDownloadUrl()` 方法来处理这个问题：

```java
private String getRealDownloadUrl(String batchUrl) {
    try {
        logger.debug("调用batch_get_tmp_download_url获取真实下载链接: {}", batchUrl);
        
        // 获取访问令牌
        String accessToken = tokenService.getAppAccessToken();
        
        // 创建URL连接并设置认证头
        URL url = new URL(batchUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestProperty("Authorization", "Bearer " + accessToken);
        
        // 处理认证失败和token刷新
        int responseCode = connection.getResponseCode();
        if (responseCode == 401 || responseCode == 403) {
            // 自动刷新token并重试
            tokenService.forceRefreshAppAccessToken();
            // ... 重试逻辑
        }
        
        // 解析JSON响应获取真实下载链接
        Map<String, Object> responseMap = JSON.parseObject(response.toString(), Map.class);
        if (responseMap.containsKey("code") && (Integer) responseMap.get("code") == 0) {
            Map<String, Object> data = (Map<String, Object>) responseMap.get("data");
            if (data != null && data.containsKey("tmp_download_urls")) {
                Map<String, Object> tmpUrls = (Map<String, Object>) data.get("tmp_download_urls");
                if (tmpUrls != null && !tmpUrls.isEmpty()) {
                    Object firstUrl = tmpUrls.values().iterator().next();
                    return firstUrl.toString(); // 返回真实的图片下载链接
                }
            }
        }
        
        return null;
    } catch (Exception e) {
        logger.error("获取真实下载链接异常", e);
        return null;
    }
}
```

### 2. 修改图片下载逻辑

修改了 `downloadImageFromUrl()` 方法，增加URL类型检测：

```java
private String downloadImageFromUrl(String imageUrl, String originalName) {
    try {
        logger.info("开始下载图片: {} from URL: {}", originalName, imageUrl);

        // 检查URL类型，如果是batch_get_tmp_download_url，需要先获取真实下载链接
        if (imageUrl.contains("batch_get_tmp_download_url")) {
            String realDownloadUrl = getRealDownloadUrl(imageUrl);
            if (realDownloadUrl == null) {
                logger.error("获取真实下载链接失败: {}", originalName);
                return null;
            }
            imageUrl = realDownloadUrl;
            logger.info("获取到真实下载链接: {}", imageUrl);
        }

        // 继续原有的图片下载逻辑...
    }
}
```

## 修复效果

### 修复前的问题
1. ❌ **Content-Type错误**: `application/json; charset=utf-8`
2. ❌ **URL类型错误**: 直接访问batch_get_tmp_download_url返回JSON
3. ❌ **99991661错误**: Missing access token for authorization
4. ❌ **重复请求**: FileUploadService重复调用导致400错误

### 修复后的改进
1. ✅ **自动获取真实链接**: 调用batch_get_tmp_download_url获取实际图片URL
2. ✅ **正确的Content-Type**: 真实链接返回 `image/png`, `image/gif` 等
3. ✅ **完整token认证**: 自动添加Authorization头并处理token刷新
4. ✅ **避免重复请求**: 直接处理图片下载和保存

### 预期的日志变化

**修复前**:
```
DEBUG c.z.service.FeishuBitableService - 图片信息 - Content-Type: application/json; charset=utf-8
ERROR c.z.service.FeishuBitableService - URL指向的不是图片文件，Content-Type: application/json; charset=utf-8
```

**修复后**:
```
DEBUG c.z.service.FeishuBitableService - 调用batch_get_tmp_download_url获取真实下载链接
INFO  c.z.service.FeishuBitableService - 获取到真实下载链接: https://actual-image-url.com/image.gif
DEBUG c.z.service.FeishuBitableService - 图片信息 - Content-Type: image/gif, Content-Length: 5290333
INFO  c.z.service.FeishuBitableService - 图片下载并保存成功: A39BAA535EF591617EE6E65CCE9C237B.gif -> http://localhost:8080/uploads/2025/07/25/20250725_123456_abc123.gif
```

## 测试验证

### 1. 启动服务器
```bash
# 重新编译和启动服务器以加载修复
mvn clean compile spring-boot:run

# 或者重启现有服务器进程
```

### 2. 运行测试脚本
```bash
# 运行自动化测试
./test-image-download-fix.sh
```

### 3. 手动测试
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/upload-to-local" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "downloadToLocal": true,
    "pageSize": 2,
    "downloadTimeout": 60,
    "maxConcurrentDownloads": 1
  }'
```

### 4. 检查结果
```bash
# 查看日志
tail -f logs/ziniao-api.log | grep -E "(getRealDownloadUrl|batch_get_tmp_download_url|saveImageFromConnection)"

# 检查上传目录
ls -la uploads/

# 查找下载的图片
find uploads -name "*.jpg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp"
```

## 成功指标

修复成功后，你应该看到：

1. **✅ 日志显示**: "获取到真实下载链接"
2. **✅ Content-Type正确**: `image/png`, `image/gif` 等
3. **✅ 文件保存成功**: "图片下载并保存成功"
4. **✅ 响应中**: `successfulDownloads > 0`
5. **✅ uploads目录**: 包含实际的图片文件

## 故障排除

如果仍然有问题：

1. **检查飞书API权限**: 确保应用有 `drive:drive:readonly` 权限
2. **检查token有效性**: 查看token服务日志
3. **检查网络连接**: 确保能访问飞书API
4. **检查磁盘空间**: 确保有足够空间保存图片

## 总结

这次修复解决了飞书图片下载的核心问题：**正确处理batch_get_tmp_download_url API的两步下载流程**。

1. **第一步**: 调用batch_get_tmp_download_url获取真实下载链接
2. **第二步**: 使用真实链接下载图片文件

现在你的飞书图片下载功能应该可以正常工作了！🎉
