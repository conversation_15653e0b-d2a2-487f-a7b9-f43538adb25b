package com.ziniao.model.feishu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Map;

/**
 * 飞书多维表格字段映射图片上传请求参数
 * 支持通过字段映射来匹配图片并回传到指定的URL字段中
 */
@ApiModel(description = "飞书多维表格字段映射图片上传请求参数")
public class FeishuFieldMappingImageRequest {

    @ApiModelProperty(value = "多维表格的唯一标识符", required = true, example = "MgDxby4r7avigssLQnVcIQzJnm1")
    @NotBlank(message = "app_token不能为空")
    private String appToken;

    @ApiModelProperty(value = "数据表的唯一标识符", required = true, example = "tbl4sH8PYHUk36K0")
    @NotBlank(message = "table_id不能为空")
    private String tableId;

    @ApiModelProperty(value = "记录ID，如果指定则只处理该记录", example = "recqwIwhc6")
    private String recordId;

    @ApiModelProperty(value = "视图的唯一标识符，不传则使用默认视图", example = "vewgI30A6c")
    private String viewId;

    @ApiModelProperty(value = "字段映射关系，key为源图片字段名，value为目标URL字段名", 
                     required = true, 
                     example = "{\"👚上装正面图\": \"👚上装正面图url\", \"👚上装背面图\": \"👚上装背面图url\"}")
    @NotEmpty(message = "字段映射关系不能为空")
    private Map<String, String> fieldMapping;

    @ApiModelProperty(value = "筛选条件，用于筛选要处理的记录")
    private String filter;

    @ApiModelProperty(value = "分页大小，最大值是 500", example = "10")
    private Integer pageSize = 100;

    @ApiModelProperty(value = "分页标记，第一次请求不填", example = "recqwIwhc6")
    private String pageToken;

    @ApiModelProperty(value = "图片下载超时时间（秒）", example = "30")
    private Integer downloadTimeout = 30;

    @ApiModelProperty(value = "最大并发下载数", example = "5")
    private Integer maxConcurrentDownloads = 5;

    @ApiModelProperty(value = "是否将本地URL写回多维表格", example = "true")
    private Boolean updateBitableWithLocalUrl = true;

    @ApiModelProperty(value = "是否返回图片的详细信息", example = "true")
    private Boolean includeImageDetails = true;

    @ApiModelProperty(value = "指定要处理的行索引（从1开始），不填则处理所有行", example = "1")
    private Integer targetRowIndex;

    // Getters and Setters
    public String getAppToken() {
        return appToken;
    }

    public void setAppToken(String appToken) {
        this.appToken = appToken;
    }

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getViewId() {
        return viewId;
    }

    public void setViewId(String viewId) {
        this.viewId = viewId;
    }

    public Map<String, String> getFieldMapping() {
        return fieldMapping;
    }

    public void setFieldMapping(Map<String, String> fieldMapping) {
        this.fieldMapping = fieldMapping;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getPageToken() {
        return pageToken;
    }

    public void setPageToken(String pageToken) {
        this.pageToken = pageToken;
    }

    public Integer getDownloadTimeout() {
        return downloadTimeout;
    }

    public void setDownloadTimeout(Integer downloadTimeout) {
        this.downloadTimeout = downloadTimeout;
    }

    public Integer getMaxConcurrentDownloads() {
        return maxConcurrentDownloads;
    }

    public void setMaxConcurrentDownloads(Integer maxConcurrentDownloads) {
        this.maxConcurrentDownloads = maxConcurrentDownloads;
    }

    public Boolean getUpdateBitableWithLocalUrl() {
        return updateBitableWithLocalUrl;
    }

    public void setUpdateBitableWithLocalUrl(Boolean updateBitableWithLocalUrl) {
        this.updateBitableWithLocalUrl = updateBitableWithLocalUrl;
    }

    public Boolean getIncludeImageDetails() {
        return includeImageDetails;
    }

    public void setIncludeImageDetails(Boolean includeImageDetails) {
        this.includeImageDetails = includeImageDetails;
    }

    public Integer getTargetRowIndex() {
        return targetRowIndex;
    }

    public void setTargetRowIndex(Integer targetRowIndex) {
        this.targetRowIndex = targetRowIndex;
    }

    @Override
    public String toString() {
        return "FeishuFieldMappingImageRequest{" +
                "appToken='" + appToken + '\'' +
                ", tableId='" + tableId + '\'' +
                ", recordId='" + recordId + '\'' +
                ", viewId='" + viewId + '\'' +
                ", fieldMapping=" + fieldMapping +
                ", filter='" + filter + '\'' +
                ", pageSize=" + pageSize +
                ", pageToken='" + pageToken + '\'' +
                ", downloadTimeout=" + downloadTimeout +
                ", maxConcurrentDownloads=" + maxConcurrentDownloads +
                ", updateBitableWithLocalUrl=" + updateBitableWithLocalUrl +
                ", includeImageDetails=" + includeImageDetails +
                ", targetRowIndex=" + targetRowIndex +
                '}';
    }
}
