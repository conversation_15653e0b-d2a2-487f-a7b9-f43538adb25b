package com.ziniao.controller;

import com.ziniao.model.feishu.*;
import com.ziniao.service.FeishuBitableService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 飞书多维表格控制器
 * 提供飞书多维表格相关的API接口
 */
@Api(tags = "飞书多维表格", description = "飞书多维表格相关接口")
@RestController
@RequestMapping("/api/feishu/bitable")
public class FeishuBitableController {

    private static final Logger logger = LoggerFactory.getLogger(FeishuBitableController.class);

    @Autowired
    private FeishuBitableService bitableService;

    /**
     * 获取多维表格记录
     */
    @ApiOperation(value = "获取多维表格记录", notes = "获取指定飞书多维表格的记录数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "获取成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 401, message = "认证失败"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/records")
    public ResponseEntity<FeishuBitableResponse> getRecords(
            @ApiParam(value = "多维表格查询请求参数", required = true)
            @Valid @RequestBody FeishuBitableRequest request) {

        try {
            logger.info("收到获取多维表格记录请求: {}", request);

            FeishuBitableResponse response = bitableService.getRecords(request);

            if (response.isSuccess()) {
                logger.info("获取多维表格记录成功，记录数: {}", 
                        response.getData() != null && response.getData().getItems() != null ? 
                        response.getData().getItems().size() : 0);
                return ResponseEntity.ok(response);
            } else {
                logger.error("获取多维表格记录失败: {}", response.getMsg());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("获取多维表格记录异常", e);
            
            FeishuBitableResponse errorResponse = new FeishuBitableResponse();
            errorResponse.setCode(500);
            errorResponse.setMsg("服务器内部错误: " + e.getMessage());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 获取多维表格中的图片
     */
    @ApiOperation(value = "获取多维表格图片", notes = "获取飞书多维表格中的图片并可选择下载到本地服务器")
    @ApiResponses({
            @ApiResponse(code = 200, message = "处理成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 401, message = "认证失败"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/images")
    public ResponseEntity<FeishuImageDownloadResponse> getImages(
            @ApiParam(value = "图片获取请求参数", required = true)
            @Valid @RequestBody FeishuImageDownloadRequest request) {

        try {
            logger.info("收到获取多维表格图片请求: {}", request);

            FeishuImageDownloadResponse response = bitableService.downloadImages(request);

            if (response.isSuccess()) {
                logger.info("获取多维表格图片成功，总图片数: {}, 成功下载: {}, 失败: {}", 
                        response.getData().getTotalImages(),
                        response.getData().getSuccessfulDownloads(),
                        response.getData().getFailedDownloads());
                return ResponseEntity.ok(response);
            } else {
                logger.error("获取多维表格图片失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("获取多维表格图片异常", e);
            return ResponseEntity.status(500).body(
                    FeishuImageDownloadResponse.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 获取指定记录的图片
     */
    @ApiOperation(value = "获取指定记录的图片", notes = "获取飞书多维表格中指定记录的图片")
    @ApiResponses({
            @ApiResponse(code = 200, message = "处理成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 401, message = "认证失败"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/images/record/{recordId}")
    public ResponseEntity<FeishuImageDownloadResponse> getRecordImages(
            @ApiParam(value = "记录ID", required = true, example = "recqwIwhc6")
            @PathVariable String recordId,
            @ApiParam(value = "图片获取请求参数", required = true)
            @Valid @RequestBody FeishuImageDownloadRequest request) {

        try {
            logger.info("收到获取指定记录图片请求，记录ID: {}, 请求参数: {}", recordId, request);

            // 设置记录ID
            request.setRecordId(recordId);

            FeishuImageDownloadResponse response = bitableService.downloadImages(request);

            if (response.isSuccess()) {
                logger.info("获取指定记录图片成功，记录ID: {}, 总图片数: {}", 
                        recordId, response.getData().getTotalImages());
                return ResponseEntity.ok(response);
            } else {
                logger.error("获取指定记录图片失败，记录ID: {}, 错误: {}", recordId, response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("获取指定记录图片异常，记录ID: {}", recordId, e);
            return ResponseEntity.status(500).body(
                    FeishuImageDownloadResponse.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 健康检查
     */
    @ApiOperation(value = "健康检查", notes = "检查飞书多维表格服务是否正常")
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        try {
            logger.debug("飞书多维表格服务健康检查");
            return ResponseEntity.ok("飞书多维表格服务运行正常");
        } catch (Exception e) {
            logger.error("飞书多维表格服务健康检查失败", e);
            return ResponseEntity.status(500).body("服务异常: " + e.getMessage());
        }
    }

    /**
     * 简化的获取多维表格图片接口（推荐使用）
     */
    @ApiOperation(value = "简化的获取多维表格图片", notes = "简化版接口，服务器自动管理token，客户端只需传递必要参数")
    @ApiResponses({
            @ApiResponse(code = 200, message = "处理成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/images/simple")
    public ResponseEntity<FeishuImageDownloadResponse> getImagesSimple(
            @ApiParam(value = "简化的图片获取请求参数", required = true)
            @Valid @RequestBody SimpleFeishuImageRequest request) {

        try {
            logger.info("收到简化的多维表格图片请求: {}", request);

            // 转换为完整的请求参数
            FeishuImageDownloadRequest fullRequest = convertToFullRequest(request);

            FeishuImageDownloadResponse response = bitableService.downloadImages(fullRequest);

            if (response.isSuccess()) {
                logger.info("简化接口获取多维表格图片成功，总图片数: {}, 成功下载: {}, 失败: {}",
                        response.getData().getTotalImages(),
                        response.getData().getSuccessfulDownloads(),
                        response.getData().getFailedDownloads());
                return ResponseEntity.ok(response);
            } else {
                logger.error("简化接口获取多维表格图片失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("简化接口获取多维表格图片异常", e);
            return ResponseEntity.status(500).body(
                    FeishuImageDownloadResponse.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 获取服务信息
     */
    @ApiOperation(value = "获取服务信息", notes = "获取飞书多维表格服务的基本信息")
    @GetMapping("/info")
    public ResponseEntity<Object> getServiceInfo() {
        try {
            logger.debug("获取飞书多维表格服务信息");

            return ResponseEntity.ok(new Object() {
                public final String service = "飞书多维表格服务";
                public final String version = "1.0.0";
                public final String description = "提供飞书多维表格数据获取和图片下载功能，自动管理token";
                public final String[] features = {
                        "获取多维表格记录",
                        "识别图片字段",
                        "批量下载图片到本地",
                        "支持分页查询",
                        "支持字段筛选",
                        "自动管理token",
                        "token过期自动重试"
                };
                public final String[] endpoints = {
                        "POST /api/feishu/bitable/images/simple - 推荐使用的简化接口",
                        "POST /api/feishu/bitable/images - 完整功能接口",
                        "POST /api/feishu/bitable/records - 获取表格记录",
                        "GET /api/feishu/bitable/health - 健康检查"
                };
            });

        } catch (Exception e) {
            logger.error("获取飞书多维表格服务信息失败", e);
            return ResponseEntity.status(500).body("获取服务信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新多维表格记录
     */
    @ApiOperation(value = "更新多维表格记录", notes = "更新指定飞书多维表格的记录数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "更新成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 401, message = "认证失败"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PutMapping("/records/{recordId}")
    public ResponseEntity<FeishuBitableUpdateResponse> updateRecord(
            @ApiParam(value = "记录ID", required = true, example = "recqwIwhc6")
            @PathVariable String recordId,
            @ApiParam(value = "多维表格更新请求参数", required = true)
            @Valid @RequestBody FeishuBitableUpdateRequest request) {

        try {
            // 设置记录ID
            request.setRecordId(recordId);

            logger.info("收到更新多维表格记录请求: {}", request);

            FeishuBitableUpdateResponse response = bitableService.updateRecord(request);

            if (response.isSuccess()) {
                logger.info("更新多维表格记录成功: recordId={}", recordId);
                return ResponseEntity.ok(response);
            } else {
                logger.error("更新多维表格记录失败: {}", response.getMsg());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("更新多维表格记录异常", e);
            return ResponseEntity.status(500).body(
                    FeishuBitableUpdateResponse.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 获取飞书多维表格图片并上传到本地服务器（增强版）
     */
    @ApiOperation(value = "获取飞书图片并上传到本地", notes = "获取飞书多维表格中的图片，下载并上传到本地服务器，避免token认证错误")
    @ApiResponses({
            @ApiResponse(code = 200, message = "处理成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 401, message = "认证失败"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/images/upload-to-local")
    public ResponseEntity<FeishuImageDownloadResponse> uploadImagesToLocal(
            @ApiParam(value = "图片获取请求参数", required = true)
            @Valid @RequestBody FeishuImageDownloadRequest request) {

        try {
            logger.info("收到飞书图片上传到本地请求: {}", request);

            // 确保下载到本地设置为true
            request.setDownloadToLocal(true);

            // 设置合理的超时时间和并发数
            if (request.getDownloadTimeout() == null || request.getDownloadTimeout() <= 0) {
                request.setDownloadTimeout(60); // 60秒超时
            }
            if (request.getMaxConcurrentDownloads() == null || request.getMaxConcurrentDownloads() <= 0) {
                request.setMaxConcurrentDownloads(3); // 限制并发数避免过载
            }

            FeishuImageDownloadResponse response = bitableService.downloadImages(request);

            if (response.isSuccess()) {
                logger.info("飞书图片上传到本地成功，总图片数: {}, 成功上传: {}, 失败: {}",
                        response.getData().getTotalImages(),
                        response.getData().getSuccessfulDownloads(),
                        response.getData().getFailedDownloads());

                // 如果有失败的图片，记录详细信息
                if (response.getData().getFailedDownloads() > 0) {
                    logger.warn("部分图片上传失败，请检查日志获取详细信息");
                }

                return ResponseEntity.ok(response);
            } else {
                logger.error("飞书图片上传到本地失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("飞书图片上传到本地异常", e);
            return ResponseEntity.status(500).body(
                    FeishuImageDownloadResponse.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 转换简化请求为完整请求
     */
    private FeishuImageDownloadRequest convertToFullRequest(SimpleFeishuImageRequest simpleRequest) {
        FeishuImageDownloadRequest fullRequest = new FeishuImageDownloadRequest();

        fullRequest.setAppToken(simpleRequest.getAppToken());
        fullRequest.setTableId(simpleRequest.getTableId());
        fullRequest.setViewId(simpleRequest.getViewId());
        fullRequest.setRecordId(simpleRequest.getRecordId());
        fullRequest.setImageFields(simpleRequest.getImageFields());
        fullRequest.setDownloadToLocal(simpleRequest.getDownloadToLocal());
        fullRequest.setFilter(simpleRequest.getFilter());
        fullRequest.setPageSize(simpleRequest.getPageSize());
        fullRequest.setPageToken(simpleRequest.getPageToken());
        fullRequest.setIncludeImageDetails(simpleRequest.getIncludeImageDetails());

        // 设置默认值
        fullRequest.setDownloadTimeout(30);
        fullRequest.setMaxConcurrentDownloads(5);

        return fullRequest;
    }

    /**
     * 处理特定的6个图片字段：downImageUrl、downOriginUrl、modelImageUrl、modelMaskImageUrl、upperImageUrl、upperOriginUrl
     * 下载图片到服务器并回写本地URL到多维表格
     */
    @ApiOperation(value = "处理特定图片字段", notes = "专门处理downImageUrl、downOriginUrl、modelImageUrl、modelMaskImageUrl、upperImageUrl、upperOriginUrl这6个图片字段，下载图片到服务器并回写本地URL")
    @ApiResponses({
            @ApiResponse(code = 200, message = "处理成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 401, message = "认证失败"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/specific-image-fields")
    public ResponseEntity<FeishuImageDownloadResponse> processSpecificImageFields(
            @ApiParam(value = "特定图片字段处理请求参数", required = true)
            @Valid @RequestBody FeishuSpecificImageFieldsRequest request) {

        try {
            logger.info("收到特定图片字段处理请求: {}", request);

            FeishuImageDownloadResponse response = bitableService.processSpecificImageFields(request);

            if (response.isSuccess()) {
                logger.info("特定图片字段处理成功，总图片数: {}, 成功下载: {}, 失败: {}",
                        response.getData().getTotalImages(),
                        response.getData().getSuccessfulDownloads(),
                        response.getData().getFailedDownloads());
                return ResponseEntity.ok(response);
            } else {
                logger.error("特定图片字段处理失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("特定图片字段处理异常", e);
            return ResponseEntity.status(500).body(
                    FeishuImageDownloadResponse.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 字段映射图片上传接口
     * 根据字段映射关系，从源图片字段下载图片并将本地URL写入目标字段
     */
    @ApiOperation(value = "字段映射图片上传",
                 notes = "根据字段映射关系，从源图片字段下载图片并将本地URL写入目标字段。" +
                        "例如：从'👚上装正面图'字段下载图片，将本地URL写入'👚上装正面图url'字段")
    @ApiResponses({
            @ApiResponse(code = 200, message = "处理成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 401, message = "认证失败"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/images/field-mapping-upload")
    public ResponseEntity<FeishuImageDownloadResponse> processFieldMappingImages(
            @ApiParam(value = "字段映射图片上传请求参数", required = true)
            @Valid @RequestBody FeishuFieldMappingImageRequest request) {

        try {
            logger.info("收到字段映射图片上传请求: {}", request);

            FeishuImageDownloadResponse response = bitableService.processFieldMappingImages(request);

            if (response.isSuccess()) {
                logger.info("字段映射图片上传成功，总图片数: {}, 成功下载: {}, 失败: {}, 字段映射: {}",
                        response.getData().getTotalImages(),
                        response.getData().getSuccessfulDownloads(),
                        response.getData().getFailedDownloads(),
                        request.getFieldMapping().size());
                return ResponseEntity.ok(response);
            } else {
                logger.error("字段映射图片上传失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("字段映射图片上传异常", e);
            return ResponseEntity.status(500).body(
                    FeishuImageDownloadResponse.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }
}
