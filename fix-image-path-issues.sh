#!/bin/bash

# 修复图片路径问题

echo "🔧 修复图片路径问题"
echo "==================="

# 配置变量
OLD_PATH="/www/wwwroot/fs.vwo50.life_998/uploads"
NEW_PATH="/www/wwwroot/fs.vwo50.life_998/jar/uploads"
LOG_FILE="/www/wwwroot/fs.vwo50.life_998/logs/ziniao-ai-demo.log"

echo ""
echo "1. 检查当前路径状态"
echo "-------------------"
echo "🔍 检查旧路径: $OLD_PATH"
if [ -d "$OLD_PATH" ]; then
    echo "✅ 旧路径存在"
    echo "📁 旧路径文件数量: $(find "$OLD_PATH" -type f 2>/dev/null | wc -l)"
    echo "📋 最近的图片文件:"
    find "$OLD_PATH" -name "*.png" -mtime -1 | head -3
else
    echo "❌ 旧路径不存在"
fi

echo ""
echo "🔍 检查新路径: $NEW_PATH"
if [ -d "$NEW_PATH" ]; then
    echo "✅ 新路径存在"
    echo "📁 新路径文件数量: $(find "$NEW_PATH" -type f 2>/dev/null | wc -l)"
else
    echo "❌ 新路径不存在，正在创建..."
    mkdir -p "$NEW_PATH"
    chmod 755 "$NEW_PATH"
    chown -R www:www "$NEW_PATH" 2>/dev/null || echo "⚠️ 无法设置所有者，请手动设置"
    echo "✅ 新路径已创建"
fi

echo ""
echo "2. 迁移现有文件"
echo "---------------"
if [ -d "$OLD_PATH" ] && [ -d "$NEW_PATH" ]; then
    echo "🚚 开始迁移文件..."
    
    # 计算需要迁移的文件数量
    FILE_COUNT=$(find "$OLD_PATH" -type f 2>/dev/null | wc -l)
    echo "📊 需要迁移的文件数量: $FILE_COUNT"
    
    if [ "$FILE_COUNT" -gt 0 ]; then
        echo "⏳ 正在迁移文件..."
        
        # 使用rsync进行迁移（如果可用）
        if command -v rsync >/dev/null 2>&1; then
            rsync -av "$OLD_PATH/" "$NEW_PATH/"
            echo "✅ 使用rsync迁移完成"
        else
            # 使用cp进行迁移
            cp -r "$OLD_PATH/"* "$NEW_PATH/" 2>/dev/null
            echo "✅ 使用cp迁移完成"
        fi
        
        # 验证迁移结果
        NEW_FILE_COUNT=$(find "$NEW_PATH" -type f 2>/dev/null | wc -l)
        echo "📊 迁移后新路径文件数量: $NEW_FILE_COUNT"
        
        if [ "$NEW_FILE_COUNT" -eq "$FILE_COUNT" ]; then
            echo "✅ 文件迁移成功"
        else
            echo "⚠️ 文件迁移可能不完整，请检查"
        fi
    else
        echo "ℹ️ 没有文件需要迁移"
    fi
else
    echo "⚠️ 无法进行文件迁移"
fi

echo ""
echo "3. 检查应用配置"
echo "---------------"
echo "📋 检查配置文件中的路径:"
grep -n "path:" application-prod.yml || echo "❌ 未找到路径配置"

echo ""
echo "📋 检查应用当前使用的路径:"
if [ -f "$LOG_FILE" ]; then
    tail -n 100 "$LOG_FILE" | grep -E "(uploadPath配置|上传文件存储路径)" | tail -3
else
    echo "❌ 日志文件不存在"
fi

echo ""
echo "4. 重启应用"
echo "-----------"
echo "🛑 停止当前应用..."
JAVA_PID=$(ps aux | grep java | grep -v grep | awk '{print $2}')
if [ -n "$JAVA_PID" ]; then
    echo "找到Java进程: $JAVA_PID"
    kill -9 $JAVA_PID
    sleep 3
    echo "✅ 应用已停止"
else
    echo "ℹ️ 未找到运行中的Java进程"
fi

echo ""
echo "🚀 启动应用..."
cd /www/wwwroot/fs.vwo50.life_998

# 确保使用正确的配置文件启动
if [ -f "ziniao-ai-demo.jar" ]; then
    nohup java -jar \
        -Dspring.profiles.active=prod \
        -Dspring.config.location=classpath:/application.yml,file:./application-prod.yml \
        -Dserver.port=18088 \
        ziniao-ai-demo.jar > app.log 2>&1 &
    
    NEW_PID=$!
    echo "🎯 应用已启动，PID: $NEW_PID"
    
    # 等待应用启动
    echo "⏳ 等待应用启动..."
    sleep 15
    
    # 检查应用是否成功启动
    if ps -p $NEW_PID > /dev/null 2>&1; then
        echo "✅ 应用启动成功"
    else
        echo "❌ 应用启动失败，检查日志:"
        tail -n 20 app.log
        exit 1
    fi
else
    echo "❌ 未找到JAR文件"
    exit 1
fi

echo ""
echo "5. 验证修复效果"
echo "---------------"
echo "⏳ 等待配置加载..."
sleep 5

echo "📋 检查新的配置是否生效:"
tail -n 50 "$LOG_FILE" | grep -E "(uploadPath配置|构建完整文件路径)" | tail -5

echo ""
echo "🧪 测试代理URL生成:"
curl -s -X POST \
  -d "filePath=/uploads/2025/07/26/test.png" \
  "http://39.108.93.224:18088/api/image-proxy/generate-url" | jq '.'

echo ""
echo "6. 创建测试文件"
echo "---------------"
echo "📝 创建测试图片文件:"
TEST_FILE="$NEW_PATH/2025/07/26/test-image.png"
mkdir -p "$(dirname "$TEST_FILE")"

# 创建一个简单的测试图片（1x1像素PNG）
echo -e '\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82' > "$TEST_FILE"

if [ -f "$TEST_FILE" ]; then
    echo "✅ 测试文件已创建: $TEST_FILE"
    
    echo ""
    echo "🧪 测试代理访问:"
    # 生成代理URL
    PROXY_RESPONSE=$(curl -s -X POST \
      -d "filePath=/uploads/2025/07/26/test-image.png" \
      "http://39.108.93.224:18088/api/image-proxy/generate-url")
    
    PROXY_URL=$(echo "$PROXY_RESPONSE" | jq -r '.proxyUrl' 2>/dev/null)
    if [ "$PROXY_URL" != "null" ] && [ -n "$PROXY_URL" ]; then
        echo "✅ 代理URL生成成功: $PROXY_URL"
        
        # 测试访问
        HTTP_CODE=$(curl -s -w "%{http_code}" -o /dev/null "$PROXY_URL")
        if [ "$HTTP_CODE" = "200" ]; then
            echo "✅ 代理URL访问成功"
        else
            echo "❌ 代理URL访问失败，HTTP状态码: $HTTP_CODE"
        fi
    else
        echo "❌ 代理URL生成失败"
    fi
else
    echo "❌ 测试文件创建失败"
fi

echo ""
echo "7. 清理工作"
echo "-----------"
echo "🧹 清理测试文件..."
rm -f "$TEST_FILE"

echo ""
echo "✅ 修复脚本执行完成"
echo ""
echo "📊 修复总结:"
echo "   1. 修复了generateImageId方法的字符串越界问题"
echo "   2. 迁移了文件到新路径: $NEW_PATH"
echo "   3. 重启了应用以加载新配置"
echo "   4. 验证了代理URL功能"
echo ""
echo "🎯 下一步:"
echo "   1. 测试特定图片字段接口"
echo "   2. 确认飞书回写使用代理URL"
echo "   3. 监控应用日志确保无错误"
echo ""
echo "📞 如果问题仍然存在:"
echo "   1. 检查文件权限: ls -la $NEW_PATH"
echo "   2. 查看应用日志: tail -f $LOG_FILE"
echo "   3. 验证配置加载: grep uploadPath $LOG_FILE"
