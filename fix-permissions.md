# 飞书应用权限配置指南

## 🎯 问题确认
你的多维表格链接是正确的，但应用缺少必要的权限来访问知识库中的多维表格。

## 🔧 解决步骤

### 步骤1: 登录飞书开放平台
访问: https://open.feishu.cn/app/cli_a8fe3e73bd78d00d

### 步骤2: 添加必要权限

在应用管理页面，找到"权限管理"，添加以下权限：

#### 多维表格权限
- ✅ `bitable:app` - 获取多维表格信息
- ✅ `bitable:app:readonly` - 读取多维表格数据

#### 知识库权限（因为你的表格在知识库中）
- ✅ `wiki:wiki:readonly` - 读取知识库内容

#### 文件权限（用于下载图片）
- ✅ `drive:drive:readonly` - 读取云文档文件

### 步骤3: 发布权限
1. 保存权限配置
2. 点击"版本管理与发布"
3. 创建新版本并发布

### 步骤4: 安装应用
确保应用已安装到包含该多维表格的飞书组织中。

### 步骤5: 验证配置
权限配置完成后，运行以下测试：

```bash
./diagnose-bitable-access.sh
```

## 📋 权限说明

| 权限 | 用途 | 必需性 |
|------|------|--------|
| `bitable:app` | 获取多维表格基本信息 | 必需 |
| `bitable:app:readonly` | 读取多维表格数据和记录 | 必需 |
| `wiki:wiki:readonly` | 访问知识库中的内容 | 推荐 |
| `drive:drive:readonly` | 下载图片文件 | 可选 |

## 🧪 测试你的配置

权限配置完成后，使用以下参数测试：

```json
{
  "appToken": "Wc2WwTiksil7vVkE1hqcmCmmneb",
  "tableId": "tbl4sH8PYHUk36K0",
  "viewId": "vewgI30A6c",
  "downloadToLocal": true,
  "pageSize": 5,
  "includeImageDetails": true
}
```

测试命令：
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "Wc2WwTiksil7vVkE1hqcmCmmneb",
    "tableId": "tbl4sH8PYHUk36K0",
    "viewId": "vewgI30A6c",
    "downloadToLocal": true,
    "pageSize": 5,
    "includeImageDetails": true
  }'
```

## ⚠️ 常见问题

### Q: 权限添加后仍然报错？
A: 
1. 确保权限已发布（不只是保存）
2. 等待几分钟让权限生效
3. 重新获取访问令牌

### Q: 如何确认权限是否生效？
A: 运行诊断脚本，检查应用信息中是否包含相关权限

### Q: 企业应用 vs 个人应用？
A: 如果是企业内部应用，确保在正确的企业租户下配置

## 🔗 相关链接

- **你的应用管理**: https://open.feishu.cn/app/cli_a8fe3e73bd78d00d
- **权限文档**: https://open.feishu.cn/document/server-docs/authentication-management/access-token/app_access_token_internal
- **多维表格API**: https://open.feishu.cn/document/server-docs/docs/bitable-v1/bitable-overview

## 📞 如果仍有问题

配置权限后如果仍有问题，请提供：
1. 权限配置截图
2. 新的错误信息
3. 诊断脚本的输出结果
