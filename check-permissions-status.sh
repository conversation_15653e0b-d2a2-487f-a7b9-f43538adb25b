#!/bin/bash

# 检查飞书应用权限状态

echo "=== 飞书应用权限状态检查 ==="
echo ""

APP_ID="cli_a8fe3e73bd78d00d"
APP_SECRET="whPto88DEdJ9n3uBiDoDNhlTEb3KAJLU"
BASE_URL="https://open.feishu.cn"
APP_TOKEN="Wc2WwTiksil7vVkE1hqcmCmmneb"

echo "📋 应用信息:"
echo "  App ID: $APP_ID"
echo "  多维表格Token: $APP_TOKEN"
echo ""

# 获取令牌
echo "🔑 获取访问令牌..."
token_response=$(curl -s -X POST "$BASE_URL/open-apis/auth/v3/app_access_token/internal" \
  -H "Content-Type: application/json" \
  -d "{\"app_id\": \"$APP_ID\", \"app_secret\": \"$APP_SECRET\"}")

if echo "$token_response" | grep -q '"code":0'; then
    app_access_token=$(echo "$token_response" | sed -n 's/.*"app_access_token":"\([^"]*\)".*/\1/p')
    echo "✅ 令牌获取成功"
else
    echo "❌ 令牌获取失败: $token_response"
    exit 1
fi

echo ""
echo "🔍 检查应用权限范围..."

# 获取应用详细信息，包括权限
app_detail_response=$(curl -s -X GET "$BASE_URL/open-apis/application/v6/applications/$APP_ID" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

echo "应用详情响应: $app_detail_response"

# 检查权限
if echo "$app_detail_response" | grep -q "bitable"; then
    echo "✅ 应用包含多维表格相关权限"
else
    echo "❌ 应用缺少多维表格权限"
fi

if echo "$app_detail_response" | grep -q "wiki"; then
    echo "✅ 应用包含知识库相关权限"
else
    echo "❌ 应用缺少知识库权限"
fi

echo ""
echo "🧪 测试多维表格访问..."

# 测试多维表格应用信息
bitable_app_response=$(curl -s -X GET "$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN" \
  -H "Authorization: Bearer $app_access_token" \
  -H "Content-Type: application/json")

echo "多维表格应用响应: $bitable_app_response"

if echo "$bitable_app_response" | grep -q '"code":0'; then
    echo "✅ 多维表格应用访问成功"
    
    # 获取表格列表
    echo ""
    echo "📋 获取表格列表..."
    tables_response=$(curl -s -X GET "$BASE_URL/open-apis/bitable/v1/apps/$APP_TOKEN/tables" \
      -H "Authorization: Bearer $app_access_token" \
      -H "Content-Type: application/json")
    
    echo "表格列表响应: $tables_response"
    
    if echo "$tables_response" | grep -q "tbl4sH8PYHUk36K0"; then
        echo "✅ 找到目标表格 tbl4sH8PYHUk36K0"
    else
        echo "❌ 未找到目标表格，可用表格:"
        echo "$tables_response" | grep -o '"table_id":"[^"]*"' | head -5
    fi
    
else
    echo "❌ 多维表格应用访问失败"
    
    # 分析错误
    error_code=$(echo "$bitable_app_response" | sed -n 's/.*"code":\([0-9]*\).*/\1/p')
    error_msg=$(echo "$bitable_app_response" | sed -n 's/.*"msg":"\([^"]*\)".*/\1/p')
    
    echo "错误码: $error_code"
    echo "错误信息: $error_msg"
    
    case $error_code in
        91402)
            echo ""
            echo "🔧 91402错误解决方案:"
            echo "1. 应用权限不足或未安装到正确组织"
            echo "2. 立即访问: https://open.feishu.cn/app/$APP_ID"
            echo "3. 检查权限配置和应用安装状态"
            echo "4. 确保应用在包含该多维表格的飞书组织中"
            ;;
        99991401)
            echo ""
            echo "🔧 IP限制解决方案:"
            echo "1. 访问: https://open.feishu.cn/app/$APP_ID"
            echo "2. 在安全设置中添加IP白名单"
            echo "3. 或暂时禁用IP限制（仅测试环境）"
            ;;
        99991664)
            echo ""
            echo "🔧 权限不足解决方案:"
            echo "1. 访问: https://open.feishu.cn/app/$APP_ID"
            echo "2. 添加权限: bitable:app, bitable:app:readonly"
            echo "3. 发布权限并等待生效"
            ;;
    esac
fi

echo ""
echo "📝 权限配置检查清单:"
echo "□ 访问应用管理: https://open.feishu.cn/app/$APP_ID"
echo "□ 添加权限: bitable:app"
echo "□ 添加权限: bitable:app:readonly"
echo "□ 添加权限: wiki:wiki:readonly"
echo "□ 保存并发布权限"
echo "□ 确认应用已安装到正确组织"
echo "□ 等待权限生效（5-10分钟）"
echo ""
echo "🔗 快捷链接:"
echo "- 应用管理: https://open.feishu.cn/app/$APP_ID"
echo "- 权限申请: https://open.feishu.cn/app/$APP_ID/auth"
echo "- 多维表格链接: https://lbit922efv.feishu.cn/wiki/$APP_TOKEN"
